import pandas as pd
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PySide6.QtCore import QThread, Signal
import time

from lightweight_charts.widgets import QtChart
from datetime import datetime, timedelta

from xtquant import xtdata

app = QApplication([])
window = QMainWindow()
layout = QVBoxLayout()
widget = QWidget()
widget.setLayout(layout)

window.resize(800, 500)
layout.setContentsMargins(0, 0, 0, 0)

chart = QtChart(widget)

#####################################################################
# 以下新增函数用于获取历史数据，不同 timeframe 调用不同的逻辑
#####################################################################

# 常量定义：分钟级数据周期数和日线数据月份数（便于统一调整）
MINUTE_CYCLE_COUNT = 240
DAILY_MONTH_COUNT = 6

# 全局缓存，用于存储已获取的历史数据，格式为： { "symbol_timeframe": DataFrame }
history_cache = {}


def fetch_minute_data(symbol):
    """获取1分钟级历史数据，返回一个按分钟排列的 DataFrame（共 MINUTE_CYCLE_COUNT 个周期）"""
    now = datetime.now()
    window_days = 7
    current_start = now - timedelta(days=window_days)

    while True:
        start_time_str = current_start.strftime("%Y%m%d") + "000000"
        end_time_str = now.strftime("%Y%m%d%H%M%S")

        # 下载当前窗口内的 1min 数据，并增加异常处理
        try:
            xtdata.download_history_data(symbol, period='1m', start_time=start_time_str, end_time=end_time_str)
            data = xtdata.get_market_data([], [symbol], period='1m', start_time=start_time_str, end_time=end_time_str,
                                          dividend_type='none')
        except Exception as e:
            err_msg = str(e)
            from PySide6.QtWidgets import QMessageBox
            if "invalid stockcode" in err_msg.lower() or "invalid stock" in err_msg.lower():
                QMessageBox.warning(None, "警告", "不是正确的股票代码")
            else:
                QMessageBox.critical(None, "错误", err_msg)
            return pd.DataFrame()

        # 将 data 内每个字段转换为列表，构造 DataFrame
        extracted = {}
        for field, df_field in data.items():
            if isinstance(df_field, pd.DataFrame) and not df_field.empty:
                extracted[field] = df_field.iloc[0].tolist()
            else:
                extracted[field] = []

        df = pd.DataFrame(extracted)

        # 如果存在 time 列，转换为 datetime 类型并排序
        if "time" in df.columns:
            try:
                df["time"] = pd.to_datetime(df["time"], unit='ms') + pd.Timedelta(hours=8)
            except Exception as e:
                print("DEBUG: Error converting time in fetch_minute_data:", e)
            # df.drop(columns=['volume'], inplace=True, errors='ignore')
            df.sort_values("time", inplace=True)

        # 检查是否达到 MINUTE_CYCLE_COUNT 个周期数据点
        if len(df) >= MINUTE_CYCLE_COUNT:
            # 取最新 MINUTE_CYCLE_COUNT 行，并格式化 time 列
            df = df.tail(MINUTE_CYCLE_COUNT)
            if "time" in df.columns:
                df["time"] = df["time"].dt.strftime("%Y-%m-%d %H:%M")
            break
        else:
            # 数据不足 MINUTE_CYCLE_COUNT 个周期，往前再扩展7天查询区间
            current_start = current_start - timedelta(days=window_days)
            print(f"DEBUG: 数据不足{MINUTE_CYCLE_COUNT}周期，扩展查询区间到 {current_start.strftime('%Y-%m-%d')} 至 {now.strftime('%Y-%m-%d %H:%M:%S')}")

    return df


def fetch_daily_data(symbol):
    """获取日线级历史数据，返回一个按日排列的 DataFrame（取最新 DAILY_MONTH_COUNT 个月数据）"""
    now = datetime.now()
    # 计算查询区间：取最新 DAILY_MONTH_COUNT 个月的数据（当月及之前 DAILY_MONTH_COUNT-1 个月）
    if now.month >= DAILY_MONTH_COUNT:
        start_year = now.year
        start_month = now.month - (DAILY_MONTH_COUNT - 1)
    else:
        start_year = now.year - 1
        start_month = now.month - (DAILY_MONTH_COUNT - 1) + 12
    # 起始日期为指定月份的第一天
    start_date = datetime(start_year, start_month, 1)
    # 结束日期取当前日期，当日结束时刻设为235959
    end_date = now
    start_time_str = start_date.strftime("%Y%m%d") + "000000"
    end_time_str = end_date.strftime("%Y%m%d") + "235959"

    xtdata.download_history_data(symbol, period='1d', start_time=start_time_str, end_time=end_time_str)
    data = xtdata.get_market_data([], [symbol], period='1d', start_time=start_time_str, end_time=end_time_str,
                                  dividend_type='none')

    extracted = {}
    for field, df_field in data.items():
        if isinstance(df_field, pd.DataFrame) and not df_field.empty:
            extracted[field] = df_field.iloc[0].tolist()
        else:
            extracted[field] = []

    df = pd.DataFrame(extracted)

    if "time" in df.columns:
        try:
            df["time"] = pd.to_datetime(df["time"], unit='ms') + pd.Timedelta(hours=8)
            df["time"] = df["time"].dt.strftime("%Y-%m-%d")
        except Exception as e:
            print("DEBUG: Error converting time in fetch_daily_data:", e)
    # df.drop(columns=['volume'], inplace=True, errors='ignore')
    if "time" in df.columns:
        df.sort_values("time", inplace=True)
    return df


def get_bar_data(symbol, timeframe):
    cache_key = f"{symbol}_{timeframe}"
    global history_cache
    if cache_key in history_cache:
        print(f"DEBUG: Using cached data for {cache_key}")
        return history_cache[cache_key]
    else:
        print(f"DEBUG: Fetching data for {cache_key}")
        if timeframe == '1min':
            new_data = fetch_minute_data(symbol)
        elif timeframe == '1day':
            new_data = fetch_daily_data(symbol)
        else:
            print("DEBUG: Unknown timeframe selected")
            return pd.DataFrame()
        history_cache[cache_key] = new_data
        return new_data


class DataUpdateThread(QThread):
    """后台数据更新线程"""
    data_updated = Signal(pd.Series)  # 信号：用于发送新数据到主线程

    def __init__(self, symbol):
        super().__init__()
        self.symbol = symbol
        self.running = True
        self.last_timestamp = None

    def run(self):
        while self.running:
            try:
                # 获取当前时间
                now = datetime.now()
                # 构造查询时间范围（最近5分钟）
                end_time = now.strftime("%Y%m%d%H%M%S")
                start_time = (now - timedelta(minutes=5)).strftime("%Y%m%d%H%M%S")

                # 下载最新数据
                xtdata.download_history_data(self.symbol, period='1m', 
                                          start_time=start_time, 
                                          end_time=end_time)
                data = xtdata.get_market_data([], [self.symbol], period='1m',
                                            start_time=start_time,
                                            end_time=end_time,
                                            dividend_type='none')

                # 转换数据格式
                extracted = {}
                for field, df_field in data.items():
                    if isinstance(df_field, pd.DataFrame) and not df_field.empty:
                        extracted[field] = df_field.iloc[0].tolist()
                    else:
                        extracted[field] = []

                df = pd.DataFrame(extracted)

                if not df.empty and "time" in df.columns:
                    # 转换时间戳
                    df["time"] = pd.to_datetime(df["time"], unit='ms') + pd.Timedelta(hours=8)
                    df["time"] = df["time"].dt.strftime("%Y-%m-%d %H:%M")
                    df = df.sort_values("time")

                    # 获取最新一条记录
                    latest_data = df.iloc[-1]
                    current_timestamp = latest_data["time"]

                    # 如果时间戳不同，更新缓存并发送更新信号
                    if current_timestamp != self.last_timestamp:
                        self.last_timestamp = current_timestamp
                        # 更新1分钟缓存数据
                        cache_key = f"{self.symbol}_1min"
                        if cache_key in history_cache:
                            cached_df = history_cache[cache_key]
                            new_row = pd.DataFrame([latest_data])
                            
                            existing_row_idx = cached_df[cached_df['time'] == latest_data['time']].index
                            if len(existing_row_idx) > 0:
                                history_cache[cache_key].loc[existing_row_idx[0]] = latest_data
                            else:
                                history_cache[cache_key] = pd.concat([cached_df, new_row], ignore_index=True)
                                if len(history_cache[cache_key]) > MINUTE_CYCLE_COUNT:
                                    history_cache[cache_key] = history_cache[cache_key].tail(MINUTE_CYCLE_COUNT)
                        
                        self.data_updated.emit(latest_data)

            except Exception as e:
                print(f"DEBUG: Error in update thread: {str(e)}")

            # 休眠3秒
            time.sleep(3)

    def stop(self):
        self.running = False


# 在初始化时就创建并启动更新线程
def start_update_thread(chart, symbol):
    if hasattr(chart, 'update_thread'):
        chart.update_thread.stop()
        chart.update_thread.wait()
    
    chart.update_thread = DataUpdateThread(symbol)
    chart.update_thread.data_updated.connect(lambda data: update_chart_data(chart, data))
    chart.update_thread.start()


def on_search(chart, searched_string):  # Called when the user searches.
    new_data = get_bar_data(searched_string, chart.topbar['timeframe'].value)
    if new_data.empty:
        return
    chart.topbar['symbol'].set(searched_string)
    chart.set(new_data)
    
    # 重启更新线程
    start_update_thread(chart, searched_string)


def on_timeframe_selection(chart):
    """顶部工具栏回调函数，根据选择的时间周期与股票代码获取数据并更新图表"""
    timeframe = chart.topbar['timeframe'].value
    symbol = chart.topbar['symbol'].value
    
    cache_key = f"{symbol}_{timeframe}"
    global history_cache
    if cache_key in history_cache:
        df = history_cache[cache_key]
        print(f"DEBUG: Using cached data for {cache_key}")
    else:
        print(f"DEBUG: Fetching data for {cache_key}")
        if timeframe == '1min':
            df = fetch_minute_data(symbol)
        elif timeframe == '1day':
            df = fetch_daily_data(symbol)
        else:
            print("DEBUG: Unknown timeframe selected")
            return
        history_cache[cache_key] = df

    chart.set(df)
    
    # 如果数据中包含 volume 列，则创建一个子图显示 volume 数据
    if "volume" in df.columns:
         # 提取 volume 数据：保留 time 和 volume 两列
         vol_df = df[['time', 'volume']].copy()
         # 对于 volume 的颜色，这里简单设置一个默认颜色（可根据需求自定义）
         vol_df['color'] = '#26a69a'
         # 创建子图，参数说明：
         # - position: 'bottom'（在主图下方）
         # - width: 1 (100%宽度), height: 0.25 (占整个窗口高度 25%)
         # - sync=True 表示子图的 timescale 同步主图
         # - sync_crosshairs_only=False, scale_candles_only=False, toolbox=False
         vol_chart = chart.create_subchart(
             position='bottom',
             width=1,
             height=0.25,
             sync=True,
             sync_crosshairs_only=False,
             scale_candles_only=False,
             toolbox=False
         )
         # 在子图上创建一个 histogram 系列显示 volume 数据
         vol_hist = vol_chart.create_histogram(
             name="volume",
             color='#26a69a',
             price_line=False,
             price_label=False,
             scale_margin_top=0.8,
             scale_margin_bottom=0
         )
         vol_hist.set(vol_df)
        #  vol_chart.legend(visible=True)
         # 可选：添加水平线指示异常量（根据实际需求启用或删除）
         vol_hist.horizontal_line(85000.0, text='Outlier Wins')
         vol_hist.horizontal_line(-85000.0, text='Outlier Losses')


def update_chart_data(chart, new_data):
    """更新图表数据的回调函数"""
    try:
        # 只有在当前显示1分钟周期时才更新图表
        if chart.topbar['timeframe'].value == '1min':
            # 更新主图数据
            chart.update(new_data)
            
            # 如果存在成交量子图，也更新成交量数据
            if hasattr(chart, '_subcharts') and chart._subcharts:
                vol_data = pd.Series({
                    'time': new_data['time'],
                    'value': new_data['volume'],
                    'color': '#26a69a'
                })
                chart._subcharts[0]._series[0].update(vol_data)
    except Exception as e:
        print(f"DEBUG: Error updating chart: {str(e)}")


chart.events.search += on_search
# 添加顶部工具栏：切换股票 symbol 和时间周期 timeframe（目前仅支持 "1min" 和 "1day"）
chart.topbar.textbox('symbol', '600895.SH')
chart.topbar.switcher('timeframe', ('1min', '1day'), default='1min', func=on_timeframe_selection)

# 初始加载默认数据（默认为1min）
on_timeframe_selection(chart)

# 启动更新线程（使用初始股票代码）
start_update_thread(chart, chart.topbar['symbol'].value)

layout.addWidget(chart.get_webview())

window.setCentralWidget(widget)
window.show()

# 在程序退出前确保清理线程
def cleanup():
    if hasattr(chart, 'update_thread'):
        chart.update_thread.stop()
        chart.update_thread.wait()

app.aboutToQuit.connect(cleanup)

app.exec_()
