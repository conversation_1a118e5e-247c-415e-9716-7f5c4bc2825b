{
	"compilerOptions": {
		"target": "ESNext",
		"useDefineForClassFields": true,
		"module": "ESNext",
		"lib": ["ESNext", "DOM"],
		"moduleResolution": "Node",
		"strict": true,
		"resolveJsonModule": true,
		"isolatedModules": true,
		"esModuleInterop": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noImplicitReturns": true,
		"skipLibCheck": true,
		"declaration": true,
		"declarationDir": "typings",
		"emitDeclarationOnly": true,
		// "outDir": "./dist"
	},
	"include": ["src"]
}
