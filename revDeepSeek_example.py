from revDeepSeek import ChatBot

chatbot = ChatBot("kLsS2JnRYOmE7z/kWUED7JT6rgSmJ8ukmJcHuHuiJfp14iyjK/A0Qz2gpwdlyzSv")

res = chatbot.create_session()
session_id = res["id"] #会话ID

res = chatbot.chat(
    session_id=session_id, # 会话ID
    prompt="你好", # 消息内容
    parent_message_id=None, # 父消息ID
    search=False, # 启用搜索
    thinking=False, # 启用深度思考
    file=None, # 文件二进制数据(上传文件需添加)
    stream=False, # 启用流式输出
)

print(res)