import baostock as bs
import pandas as pd

def get_all_stock_names():
    """
    使用BaoStock库获取所有A股的股票名称并打印
    """
    # 登陆系统
    lg = bs.login()
    print('登录响应错误代码:', lg.error_code)
    print('登录响应错误信息:', lg.error_msg)

    if lg.error_code != '0':
        print('登录失败，程序退出')
        return

    try:
        # 获取所有股票的基本资料（当参数为空时，输出全部股票的基本信息）
        rs = bs.query_stock_basic()
        print('查询股票基本资料响应错误代码:', rs.error_code)
        print('查询股票基本资料响应错误信息:', rs.error_msg)

        if rs.error_code != '0':
            print('查询失败，程序退出')
            return

        # 收集所有数据
        data_list = []
        while (rs.error_code == '0') & rs.next():
            # 获取一条记录，将记录合并在一起
            data_list.append(rs.get_row_data())

        # 转换为DataFrame
        result = pd.DataFrame(data_list, columns=rs.fields)

        # 筛选出A股股票（type=1表示股票，status=1表示上市状态）
        a_stocks = result[(result['type'] == '1') & (result['status'] == '1')]

        # 进一步筛选出A股（以sh.6或sz.0、sz.3开头的股票代码）
        a_stocks = a_stocks[
            a_stocks['code'].str.startswith(('sh.6', 'sz.0', 'sz.3'))
        ]

        print(f"\n=== 所有A股股票名称 (共{len(a_stocks)}只) ===")
        print("-" * 60)

        # 按股票代码排序
        a_stocks_sorted = a_stocks.sort_values('code')

        # 创建用于保存到Excel的数据列表
        excel_data = []

        # 打印股票代码和名称（去掉前缀）
        for index, row in a_stocks_sorted.iterrows():
            # 去掉前缀，只保留数字部分
            stock_code = row['code'].split('.')[1] if '.' in row['code'] else row['code']
            # 去掉股票名称中ST前面的星号*
            stock_name = row['code_name'].replace('*', '')
            print(f"{stock_code:<8} {stock_name}")

            # 添加到Excel数据列表
            excel_data.append({
                '股票代码': stock_code,
                '股票名称': stock_name
            })

        print("-" * 60)
        print(f"总计: {len(a_stocks)}只A股股票")

        # 保存到Excel文件
        try:
            excel_df = pd.DataFrame(excel_data)
            excel_filename = "A股股票名单.xlsx"
            excel_df.to_excel(excel_filename, index=False, engine='openpyxl')
            print(f"\n数据已保存到 {excel_filename} 文件")
            print(f"Excel文件包含 {len(excel_data)} 条股票记录")
        except ImportError:
            print("\n警告：未安装openpyxl库，无法保存Excel文件")
            print("请运行: pip install openpyxl")
            # 备选方案：保存为CSV文件
            csv_filename = "A股股票名单.csv"
            excel_df.to_csv(csv_filename, encoding="utf-8-sig", index=False)
            print(f"已保存为CSV文件: {csv_filename}")
        except Exception as e:
            print(f"\n保存Excel文件时出错: {e}")
            # 备选方案：保存为CSV文件
            csv_filename = "A股股票名单.csv"
            excel_df.to_csv(csv_filename, encoding="utf-8-sig", index=False)
            print(f"已保存为CSV文件: {csv_filename}")

    except Exception as e:
        print(f"程序执行过程中出现错误: {e}")

    finally:
        # 登出系统
        bs.logout()
        print("\n已登出BaoStock系统")

if __name__ == "__main__":
    get_all_stock_names()