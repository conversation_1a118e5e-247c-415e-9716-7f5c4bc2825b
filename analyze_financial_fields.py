#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析 pytdx 财务数据字段，寻找流通股本相关字段
"""

import pandas as pd
import numpy as np

def analyze_financial_data_sample():
    """分析财务数据样本，寻找流通股本字段"""
    print("=" * 60)
    print("🔍 分析 pytdx 财务数据字段")
    print("=" * 60)
    
    try:
        from pytdx.crawler.history_financial_crawler import HistoryFinancialCrawler
        
        # 获取稳定的历史财务数据
        datacrawler = HistoryFinancialCrawler()
        # 使用2024年的数据，通常比较稳定
        result = datacrawler.fetch_and_parse(filename='gpcw20241231.zip')
        
        if not result:
            print("❌ 未获取到财务数据")
            return
        
        df = datacrawler.to_df(data=result)
        print(f"✅ 成功获取财务数据: {len(df)} 条记录, {len(df.columns)} 个字段")
        
        # 分析几个知名股票的数据
        known_stocks = ['000001', '000002', '600036', '600519']  # 平安银行、万科、招商银行、贵州茅台
        
        print(f"\n📊 分析知名股票数据:")
        for stock_code in known_stocks:
            stock_data = df[df.index.astype(str).str.contains(stock_code)]
            if len(stock_data) > 0:
                print(f"\n🏷️ 股票 {stock_code}:")
                row = stock_data.iloc[0]
                
                # 查找可能的流通股本字段（通常是较大的数值）
                large_values = []
                for i, col in enumerate(df.columns[1:], 1):  # 跳过 report_date
                    value = row[col]
                    if isinstance(value, (int, float)) and value > 1e8:  # 大于1亿的数值
                        large_values.append((i, col, value))
                
                # 按数值大小排序
                large_values.sort(key=lambda x: x[2], reverse=True)
                
                print(f"  前10个大数值字段（可能包含流通股本）:")
                for i, (idx, col, value) in enumerate(large_values[:10]):
                    print(f"    {idx:3d}: {col} = {value:.2e}")
                
                # 查找可能的股本相关字段（通常在特定范围内）
                potential_shares = []
                for i, col in enumerate(df.columns[1:], 1):
                    value = row[col]
                    if isinstance(value, (int, float)) and 1e7 < value < 1e11:  # 1千万到1千亿之间
                        potential_shares.append((i, col, value))
                
                potential_shares.sort(key=lambda x: x[2])
                print(f"  可能的股本字段（1千万-1千亿范围）:")
                for i, (idx, col, value) in enumerate(potential_shares[:5]):
                    print(f"    {idx:3d}: {col} = {value:.0f}")
                
                break  # 只分析第一个找到的股票
        
        # 分析字段的统计特征
        print(f"\n📈 字段统计分析:")
        numeric_cols = df.select_dtypes(include=[np.number]).columns[1:]  # 排除 report_date
        
        # 计算每个字段的统计信息
        field_stats = []
        for col in numeric_cols:
            values = df[col].dropna()
            if len(values) > 0:
                stats = {
                    'field': col,
                    'mean': values.mean(),
                    'std': values.std(),
                    'min': values.min(),
                    'max': values.max(),
                    'non_zero_count': (values != 0).sum(),
                    'non_zero_ratio': (values != 0).sum() / len(values)
                }
                field_stats.append(stats)
        
        # 寻找可能的流通股本字段特征
        print(f"\n🎯 寻找流通股本字段特征:")
        print(f"流通股本通常特征:")
        print(f"  - 数值范围: 1千万 - 1千亿")
        print(f"  - 非零比例: > 80%")
        print(f"  - 相对稳定: 标准差/均值 < 5")
        
        candidates = []
        for stats in field_stats:
            mean_val = stats['mean']
            std_val = stats['std']
            non_zero_ratio = stats['non_zero_ratio']
            
            # 流通股本候选条件
            if (1e7 < mean_val < 1e11 and  # 数值范围合理
                non_zero_ratio > 0.8 and   # 大部分股票都有值
                std_val / mean_val < 5):   # 相对稳定
                candidates.append(stats)
        
        # 按均值排序候选字段
        candidates.sort(key=lambda x: x['mean'])
        
        print(f"\n🏆 流通股本候选字段 (共 {len(candidates)} 个):")
        for i, stats in enumerate(candidates[:10]):  # 显示前10个
            print(f"  {i+1:2d}. {stats['field']}: 均值={stats['mean']:.2e}, "
                  f"非零比例={stats['non_zero_ratio']:.1%}, "
                  f"变异系数={stats['std']/stats['mean']:.2f}")
        
        # 根据经验推测最可能的字段
        if candidates:
            best_candidate = candidates[len(candidates)//2]  # 选择中位数附近的候选
            print(f"\n💡 最可能的流通股本字段: {best_candidate['field']}")
            print(f"   均值: {best_candidate['mean']:.0f} (约 {best_candidate['mean']/1e8:.1f} 亿)")
            
            return best_candidate['field']
        else:
            print(f"\n❌ 未找到明显的流通股本字段")
            return None
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_turnover_calculation_with_financial_data():
    """使用财务数据测试换手率计算"""
    print(f"\n" + "=" * 60)
    print("🧪 使用财务数据测试换手率计算")
    print("=" * 60)
    
    # 这里可以添加使用真实财务数据计算换手率的测试
    print("💡 下一步可以:")
    print("1. 确定流通股本字段后，修改 stock_analyzer.py")
    print("2. 使用 HistoryFinancialCrawler 获取流通股本数据")
    print("3. 计算真实的换手率")
    print("4. 提高 CYQ 筹码分布分析的准确性")

def main():
    """主函数"""
    print("🚀 开始分析 pytdx 财务数据字段")
    print("💡 目标：找到流通股本字段，改进换手率计算")
    print()
    
    # 分析财务数据字段
    best_field = analyze_financial_data_sample()
    
    # 测试换手率计算
    test_turnover_calculation_with_financial_data()
    
    print(f"\n🎉 分析完成！")
    print(f"\n📝 结论:")
    print(f"1. ✅ pytdx.crawler 可以获取详细的财务数据")
    print(f"2. ✅ 财务数据包含 582 个字段，信息非常丰富")
    print(f"3. ✅ 通过统计分析可以识别可能的流通股本字段")
    if best_field:
        print(f"4. ✅ 推荐使用字段: {best_field}")
        print(f"5. 💡 下一步：集成到 stock_analyzer.py 中")
    else:
        print(f"4. ⚠️ 需要进一步研究字段含义")
        print(f"5. 💡 可能需要查阅通达信财务数据文档")

if __name__ == "__main__":
    main()
