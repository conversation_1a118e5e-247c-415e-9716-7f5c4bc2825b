/* /////////////////////////////////////////////////////////////////////////////////////////////////

SET APP STYLESHEET - FULL STYLES HERE
DARK THEME - DRACULA COLOR BASED

# BY: WANDERSON M.PIMENTA
# PROJECT MADE WITH: Qt Designer and PySide6
# V: 1.0.0
#
# This project can be used freely for all uses, as long as they maintain the
# respective credits only in the Python scripts, any information in the visual
# interface (GUI) can be modified without any implication.
#
# There are limitations on Qt licenses if you want to use your products
# commercially, I recommend reading them on the official website:
# https://doc.qt.io/qtforpython/licenses.html

///////////////////////////////////////////////////////////////////////////////////////////////// */

QWidget{
	color: #333;
	font: 10pt "Segoe UI";
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Tooltip */
QToolTip {
	color: #333;
	background-color: #f8f8f2;
	border: 1px solid #CCC;
	background-image: none;
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 2px solid rgb(255, 121, 198);
	text-align: left;
	padding-left: 8px;
	margin: 0px;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Bg App */
#bgApp {	
	background-color: #f8f8f2;
	border: 1px solid #CCC;
    color: #44475a;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Left Menu */
#leftMenuBg {	
	background-color: #6272a4;
}
#topLogo {
	background-color: #6272a4;
	background-image: url(:/images/images/images/PyDracula.png);
	background-position: centered;
	background-repeat: no-repeat;
}
#titleLeftApp { font: 63 12pt "Segoe UI Semibold"; color: #f8f8f2; }
#titleLeftDescription { font: 8pt "Segoe UI"; color: #bd93f9; }

/* MENUS */
#topMenu .QPushButton {	
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color: transparent;
	text-align: left;
	padding-left: 44px;
    color: #f8f8f2;
}
#topMenu .QPushButton:hover {
	background-color: #bd93f9;
}
#topMenu .QPushButton:pressed {	
	background-color: #ff79c6;
	color: rgb(255, 255, 255);
}
#bottomMenu .QPushButton {	
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 20px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
    color: #f8f8f2;
}
#bottomMenu .QPushButton:hover {
	background-color: #bd93f9;
}
#bottomMenu .QPushButton:pressed {	
	background-color: #ff79c6;
	color: rgb(255, 255, 255);
}
#leftMenuFrame{
	border-top: 3px solid #6a7cb1;
}

/* Toggle Button */
#toggleButton {
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 20px solid transparent;
	background-color: #5b6996;
	text-align: left;
	padding-left: 44px;
	color: #f8f8f2;
}
#toggleButton:hover {
	background-color: #bd93f9;
}
#toggleButton:pressed {	
	background-color: #ff79c6;
	color: rgb(255, 255, 255);
}

/* Title Menu */
#titleRightInfo { padding-left: 10px; }


/* /////////////////////////////////////////////////////////////////////////////////////////////////
Extra Tab */
#extraLeftBox {	
	background-color: #495474;
    color: #f8f8f2;
}
#extraTopBg{	
	background-color: rgb(189, 147, 249)
}

/* Icon */
#extraIcon {
	background-position: center;
	background-repeat: no-repeat;
	background-image: url(:/icons/images/icons/icon_settings.png);
}

/* Label */
#extraLabel { color: rgb(255, 255, 255); }

/* Btn Close */
#extraCloseColumnBtn { background-color: rgba(255, 255, 255, 0); border: none;  border-radius: 5px; }
#extraCloseColumnBtn:hover { background-color: rgb(196, 161, 249); border-style: solid; border-radius: 4px; }
#extraCloseColumnBtn:pressed { background-color: rgb(180, 141, 238); border-style: solid; border-radius: 4px; }

/* Extra Content */
#extraContent{
	border-top: 3px solid #6272a4;
}

/* Extra Top Menus */
#extraTopMenu .QPushButton {
    background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
    color: #f8f8f2;
}
#extraTopMenu .QPushButton:hover {
	background-color: #5d6c99;
}
#extraTopMenu .QPushButton:pressed {	
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Content App */
#contentTopBg{	
	background-color: #6272a4;
}
#contentBottom{
	border-top: 3px solid #bd93f9;
}
#titleRightInfo{
    color: #f8f8f2;
}

/* Top Buttons */
#rightButtons .QPushButton { background-color: rgba(255, 255, 255, 0); border: none;  border-radius: 5px; }
#rightButtons .QPushButton:hover { background-color: #bd93f9; border-style: solid; border-radius: 4px; }
#rightButtons .QPushButton:pressed { background-color: #ff79c6; border-style: solid; border-radius: 4px; }

/* Theme Settings */
#extraRightBox { background-color: #495474; }
#themeSettingsTopDetail { background-color: #6272a4; }

/* Bottom Bar */
#bottomBar { background-color: #495474 }
#bottomBar QLabel { font-size: 11px; color: #f8f8f2; padding-left: 10px; padding-right: 10px; padding-bottom: 2px; }

/* CONTENT SETTINGS */
/* MENUS */
#contentSettings .QPushButton {
    background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
    color: #f8f8f2;
}
#contentSettings .QPushButton:hover {
	background-color: #5d6c99;
}
#contentSettings .QPushButton:pressed {	
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}
/* /////////////////////////////////////////////////////////////////////////////////////////////////
QTableWidget */
QTableWidget {	
	background-color: transparent;
	padding: 10px;
	border-radius: 5px;
	gridline-color: #9faeda;
    outline: none;
}
QTableWidget::item{
	border-color: #9faeda;
	padding-left: 5px;
	padding-right: 5px;
	gridline-color: #9faeda;
}
QTableWidget::item:selected{
	background-color: rgb(189, 147, 249);
    color: #f8f8f2;
}
QHeaderView::section{
	background-color: #6272a4;
	max-width: 30px;
	border: none;
	border-style: none;
}
QTableWidget::horizontalHeader {	
	background-color: #6272a4;
}
QHeaderView::section:horizontal
{
    border: 1px solid #6272a4;
	background-color: #6272a4;
	padding: 3px;
	border-top-left-radius: 7px;
    border-top-right-radius: 7px;
    color: #f8f8f2;
}
QHeaderView::section:vertical
{
    border: 1px solid #6272a4;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
LineEdit */
QLineEdit {
	background-color: #6272a4;
	border-radius: 5px;
	border: 2px solid #6272a4;
	padding-left: 10px;
	selection-color: rgb(255, 255, 255);
	selection-background-color: rgb(255, 121, 198);
    color: #f8f8f2;
}
QLineEdit:hover {
	border: 2px solid rgb(64, 71, 88);
}
QLineEdit:focus {
	border: 2px solid #ff79c6;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
PlainTextEdit */
QPlainTextEdit {
	background-color: #6272a4;
	border-radius: 5px;
	padding: 10px;
	selection-color: rgb(255, 255, 255);
	selection-background-color: rgb(255, 121, 198);
    color: #f8f8f2;
}
QPlainTextEdit  QScrollBar:vertical {
    width: 8px;
 }
QPlainTextEdit  QScrollBar:horizontal {
    height: 8px;
 }
QPlainTextEdit:hover {
	border: 2px solid rgb(64, 71, 88);
}
QPlainTextEdit:focus {
	border: 2px solid #ff79c6;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
ScrollBars */
QScrollBar:horizontal {
    border: none;
    background: #6272a4;
    height: 8px;
    margin: 0px 21px 0 21px;
	border-radius: 0px;
}
QScrollBar::handle:horizontal {
    background: rgb(189, 147, 249);
    min-width: 25px;
	border-radius: 4px
}
QScrollBar::add-line:horizontal {
    border: none;
    background: #6272a4;
    width: 20px;
	border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    subcontrol-position: right;
    subcontrol-origin: margin;
}
QScrollBar::sub-line:horizontal {
    border: none;
    background: #6272a4;
    width: 20px;
	border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    subcontrol-position: left;
    subcontrol-origin: margin;
}
QScrollBar::up-arrow:horizontal, QScrollBar::down-arrow:horizontal
{
     background: none;
}
QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal
{
     background: none;
}
 QScrollBar:vertical {
	border: none;
    background-color: #6272a4;
    width: 8px;
    margin: 21px 0 21px 0;
	border-radius: 0px;
 }
 QScrollBar::handle:vertical {	
	background: rgb(189, 147, 249);
    min-height: 25px;
	border-radius: 4px
 }
 QScrollBar::add-line:vertical {
     border: none;
    background: #6272a4;
     height: 20px;
	border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
     subcontrol-position: bottom;
     subcontrol-origin: margin;
 }
 QScrollBar::sub-line:vertical {
	border: none;
    background: #6272a4;
     height: 20px;
	border-top-left-radius: 4px;
    border-top-right-radius: 4px;
     subcontrol-position: top;
     subcontrol-origin: margin;
 }
 QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
     background: none;
 }

 QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
     background: none;
 }

/* /////////////////////////////////////////////////////////////////////////////////////////////////
CheckBox */
QCheckBox::indicator {
    border: 3px solid #6272a4;
	width: 15px;
	height: 15px;
	border-radius: 10px;
    background: #6272a4;
}
QCheckBox::indicator:hover {
    border: 3px solid rgb(119, 136, 187);
}
QCheckBox::indicator:checked {
    background: 3px solid #bd93f9;
	border: 3px solid #bd93f9;	
	background-image: url(:/icons/images/icons/cil-check-alt.png);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
RadioButton */
QRadioButton::indicator {
    border: 3px solid #6272a4;
	width: 15px;
	height: 15px;
	border-radius: 10px;
    background: #6272a4;
}
QRadioButton::indicator:hover {
    border: 3px solid rgb(119, 136, 187);
}
QRadioButton::indicator:checked {
    background: 3px solid #bd93f9;
	border: 3px solid #bd93f9;	
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
ComboBox */
QComboBox{
	background-color: #6272a4;
	border-radius: 5px;
	border: 2px solid #6272a4;
	padding: 5px;
	padding-left: 10px;
    color: #f8f8f2;
}
QComboBox:hover{
	border: 2px solid #7284b9;
}
QComboBox::drop-down {
	subcontrol-origin: padding;
	subcontrol-position: top right;
	width: 25px; 
	border-left-width: 3px;
	border-left-color: #6272a4;
	border-left-style: solid;
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;	
	background-image: url(:/icons/images/icons/cil-arrow-bottom.png);
	background-position: center;
	background-repeat: no-reperat;
 }
QComboBox QAbstractItemView {
	color: rgb(255, 121, 198);	
	background-color: #6272a4;
	padding: 10px;
	selection-background-color: #6272a4;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Sliders */
QSlider::groove:horizontal {
    border-radius: 5px;
    height: 10px;
	margin: 0px;
	background-color: #6272a4;
}
QSlider::groove:horizontal:hover {
	background-color: #6272a4;
}
QSlider::handle:horizontal {
    background-color: rgb(189, 147, 249);
    border: none;
    height: 10px;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}
QSlider::handle:horizontal:hover {
    background-color: rgb(195, 155, 255);
}
QSlider::handle:horizontal:pressed {
    background-color: rgb(255, 121, 198);
}

QSlider::groove:vertical {
    border-radius: 5px;
    width: 10px;
    margin: 0px;
	background-color: #6272a4;
}
QSlider::groove:vertical:hover {
	background-color: #6272a4;
}
QSlider::handle:vertical {
    background-color: rgb(189, 147, 249);
	border: none;
    height: 10px;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}
QSlider::handle:vertical:hover {
    background-color: rgb(195, 155, 255);
}
QSlider::handle:vertical:pressed {
    background-color: rgb(255, 121, 198);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
CommandLinkButton */
#pagesContainer QCommandLinkButton {	
	color: rgb(255, 121, 198);
	border-radius: 5px;
	padding: 5px;
    border: 2px solid #ff79c6;
    color: #ff79c6;
}
#pagesContainer QCommandLinkButton:hover {	
	color: rgb(255, 170, 255);
	background-color: #6272a4;
}
#pagesContainer QCommandLinkButton:pressed {	
	color: rgb(189, 147, 249);
	background-color: #586796;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Button */
#pagesContainer QPushButton {
	border: 2px solid #6272a4;
	border-radius: 5px;	
	background-color: #6272a4;
    color: #f8f8f2;
}
#pagesContainer QPushButton:hover {
	background-color: #7082b6;
	border: 2px solid #7082b6;
}
#pagesContainer QPushButton:pressed {	
	background-color: #546391;
	border: 2px solid #ff79c6;
}


