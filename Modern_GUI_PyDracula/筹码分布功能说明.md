# 筹码分布功能使用说明

## 功能概述

本次更新在`show_news_content_dialog`函数中集成了筹码分布图功能，使用QDialog + FigureCanvas方案，基于参考文章的筹码分布算法实现。

## 主要特性

### 1. 增强版筹码分布分析器 (ChipDistributionAnalyzer)

基于参考文章 https://blog.csdn.net/liuhui244/article/details/145965706 的实现：

- **三角形分布算法**: 使用三角形分布模拟单日筹码分布
- **筹码衰减机制**: 根据换手率计算历史筹码的衰减
- **多项指标计算**: 
  - 获利比例
  - 平均成本
  - 90%/70%成本区间
  - 筹码集中度

### 2. 可视化图表

- **水平柱状图**: 显示不同价格区间的筹码分布
- **颜色区分**: 红色表示获利筹码，绿色表示套牢筹码
- **关键指标标注**: 在图表右侧显示详细的分析指标
- **主力成本标注**: 用虚线标出主要筹码峰位置

### 3. GUI集成

- **对话框布局**: 左侧显示新闻内容，右侧显示筹码分布图
- **异步加载**: 使用后台线程生成图表，避免界面卡顿
- **实时数据**: 通过pytdx API获取真实的股票OHLC和换手率数据

## 使用方法

### 1. 查看筹码分布图

1. 启动程序后，等待新闻数据加载完成
2. 在新闻表格中，点击任意新闻内容（第4列）
3. 弹出的对话框会显示：
   - 左侧：新闻详细内容
   - 右侧：对应股票的筹码分布图
4. 图表会在后台异步生成，显示"正在加载筹码分布图..."状态

### 2. 图表解读

#### 筹码分布图
- **X轴**: 筹码数量（相对比例）
- **Y轴**: 价格区间
- **红色柱子**: 获利筹码（成本低于当前价格）
- **绿色柱子**: 套牢筹码（成本高于当前价格）
- **红色虚线**: 主力成本区域（筹码最密集的价格）

#### 关键指标
- **当前股价**: 最新收盘价
- **获利比例**: 低于当前价格的筹码占比
- **平均成本**: 所有筹码的加权平均成本
- **90%成本区间**: 90%筹码的价格分布范围
- **70%成本区间**: 70%筹码的价格分布范围
- **集中度**: 筹码集中程度，数值越小越集中

## 技术实现

### 1. 核心算法

```python
class ChipDistributionAnalyzer:
    def _calculate_k_line_contribution(self, kline, distribution, ...):
        # 三角形分布计算
        # 筹码衰减处理
        # 新筹码分布添加
```

### 2. 数据获取

- **股票数据**: 使用pytdx API获取90天的日K线数据
- **流通股本**: 优先使用akshare获取，备用pytdx估算
- **换手率计算**: volume / (流通股本 * 100) * 100%

### 3. 可视化

- **matplotlib**: 使用Figure和FigureCanvas
- **中文支持**: 设置SimHei字体
- **交互式**: 支持缩放和平移

## 测试验证

运行测试脚本验证功能：

```bash
python test_chip_distribution.py
```

测试包括：
1. 基础筹码分布计算（使用模拟数据）
2. 股票分析器增强版功能（使用真实数据）

## 注意事项

### 1. 数据依赖
- 需要网络连接获取股票数据
- 依赖pytdx和akshare库
- 需要通达信服务器连接

### 2. 性能考虑
- 图表生成在后台线程执行
- 首次加载可能需要10-30秒
- 建议在网络良好的环境下使用

### 3. 错误处理
- 网络连接失败时显示错误信息
- 股票代码无法识别时提供相似股票提示
- 数据获取失败时回退到简单对话框

## 更新日志

### v1.0 (当前版本)
- ✅ 集成ChipDistributionAnalyzer类
- ✅ 修改show_news_content_dialog函数
- ✅ 添加异步图表生成
- ✅ 支持真实股票数据分析
- ✅ 完整的错误处理机制

## 未来改进

1. **缓存机制**: 缓存已分析的股票数据
2. **更多指标**: 添加筹码峰识别、支撑阻力位分析
3. **历史对比**: 显示筹码分布的历史变化
4. **导出功能**: 支持图表导出为图片文件
