# GLM-Z1-Flash 客户端使用指南

## 概述

这是一个基于智谱AI GLM-Z1-Flash模型的Python客户端，提供简单易用的非流式调用接口。该客户端专为项目中的AI功能需求而设计，支持聊天对话、情感分析等功能。

## 特性

- ✅ 简单易用的API接口
- ✅ 非流式调用（同步调用）
- ✅ 完善的错误处理机制
- ✅ 内置情感分析功能
- ✅ 支持自定义参数
- ✅ 连接测试功能

## 安装依赖

```bash
pip install requests
```

## 快速开始

### 1. 基本使用

```python
from modules.glm_z1_client import create_glm_client

# 创建客户端
api_key = "你的API密钥"
client = create_glm_client(api_key)

# 简单聊天
response = client.simple_chat("你好，请介绍一下你自己")
print(response)
```

### 2. 情感分析

```python
# 分析新闻情感
news_text = "某公司发布了创新产品，预计将提升业绩"
sentiment = client.sentiment_analysis(news_text)
print(f"情感分析结果: {sentiment}")  # 输出: 正面/负面/未知
```

### 3. 自定义参数

```python
# 使用自定义参数
response = client.chat(
    prompt="请分析当前AI发展趋势",
    system_prompt="你是一个专业的AI技术分析师",
    temperature=0.3,  # 降低随机性
    max_tokens=200,   # 限制输出长度
    top_p=0.9
)
```

## API 参考

### GLMZ1Client 类

#### 初始化
```python
client = GLMZ1Client(api_key="你的API密钥")
```

#### 主要方法

##### `simple_chat(prompt: str) -> str`
简单聊天接口，使用默认参数。

**参数:**
- `prompt`: 用户输入的提示词

**返回:** 模型生成的回复内容

##### `chat(prompt, system_prompt=None, temperature=0.7, max_tokens=None, top_p=0.9) -> str`
完整的聊天接口，支持自定义参数。

**参数:**
- `prompt`: 用户输入的提示词
- `system_prompt`: 系统提示词（可选）
- `temperature`: 温度参数，控制随机性 (0.0-1.0)
- `max_tokens`: 最大生成token数（可选）
- `top_p`: top_p参数，控制多样性 (0.0-1.0)

##### `sentiment_analysis(text: str) -> str`
情感分析专用接口。

**参数:**
- `text`: 要分析的文本

**返回:** '正面'、'负面' 或 '未知'

##### `test_connection() -> bool`
测试API连接是否正常。

**返回:** 连接是否成功

## 错误处理

客户端使用 `GLMError` 异常来处理各种错误情况：

```python
from modules.glm_z1_client import GLMError

try:
    response = client.simple_chat("你好")
except GLMError as e:
    print(f"API调用失败: {e}")
    # 处理错误...
```

## 在其他模块中使用

### 方法1: 直接导入

```python
from modules.glm_z1_client import GLMZ1Client, GLMError

class MyAnalyzer:
    def __init__(self, api_key):
        self.glm_client = GLMZ1Client(api_key)
    
    def analyze_text(self, text):
        try:
            return self.glm_client.sentiment_analysis(text)
        except GLMError as e:
            return '未知'
```

### 方法2: 通过模块导入

```python
from modules import GLMZ1Client, create_glm_client, GLMError

# 使用便捷函数创建客户端
client = create_glm_client("你的API密钥")
```

## 配置说明

### API密钥
当前使用的API密钥: `696316314d42490c818e3e5ded184701.xhgEGx1ZbrAsBFy7`

### 模型信息
- 模型名称: `glm-z1-flash`
- API端点: `https://open.bigmodel.cn/api/paas/v4/chat/completions`
- 调用方式: 非流式（同步）

### 默认参数
- `temperature`: 0.7
- `top_p`: 0.9
- `timeout`: 60秒

## 使用示例

查看 `glm_usage_example.py` 文件获取更多详细的使用示例，包括：

- 新闻分析器
- 股票分析器  
- 聊天助手

## 测试

运行测试脚本验证客户端功能：

```bash
python test_glm_client.py
```

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要在代码中硬编码
2. **网络连接**: 确保网络连接正常，API调用需要访问外网
3. **错误处理**: 建议在所有API调用处添加异常处理
4. **调用频率**: 注意API调用频率限制，避免过于频繁的请求
5. **内容过滤**: GLM模型有内容安全过滤，某些敏感内容可能被拒绝

## 更新日志

### v1.0.0 (2025-07-01)
- 初始版本发布
- 支持基本聊天功能
- 支持情感分析
- 完善的错误处理机制
- 连接测试功能

## 技术支持

如有问题或建议，请参考：
- [智谱AI官方文档](https://bigmodel.cn/dev/api/Reasoning-models/glm-z1)
- 项目内部技术文档
