"""
GLM-Z1-Flash 模型调用客户端
基于智谱AI的GLM-Z1-Flash模型，提供简单易用的非流式调用接口
"""

import requests
import json
import time
from typing import Dict, Optional, List


class GLMError(Exception):
    """GLM API错误"""
    def __init__(self, message: str, error_code: str = None):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

    def __str__(self):
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message


class GLMZ1Client:
    """GLM-Z1-Flash 客户端类"""
    
    def __init__(self, api_key: str):
        """
        初始化GLM-Z1-Flash客户端
        
        Args:
            api_key (str): 智谱AI的API密钥
        """
        self.api_key = api_key
        self.base_url = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
        self.model = "glm-z1-flash"
        self.timeout = 60  # 请求超时时间（秒）
        
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
    
    def _make_request(self, messages: List[Dict], **kwargs) -> Dict:
        """
        发送API请求
        
        Args:
            messages (List[Dict]): 消息列表
            **kwargs: 其他参数
            
        Returns:
            Dict: API响应结果
            
        Raises:
            GLMError: API调用失败时抛出
        """
        payload = {
            "model": self.model,
            "messages": messages,
            "stream": False,  # 非流式调用
            **kwargs
        }
        
        try:
            response = requests.post(
                self.base_url,
                headers=self._get_headers(),
                json=payload,
                timeout=self.timeout
            )
            
            # 检查HTTP状态码
            if response.status_code != 200:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                raise GLMError(error_msg, str(response.status_code))
            
            # 解析JSON响应
            try:
                result = response.json()
            except json.JSONDecodeError as e:
                raise GLMError(f"JSON解析失败: {str(e)}")
            
            # 检查API错误
            if "error" in result:
                error_info = result["error"]
                error_msg = error_info.get("message", "未知错误")
                error_code = error_info.get("code", "UNKNOWN")
                raise GLMError(error_msg, error_code)
            
            return result
            
        except requests.exceptions.Timeout:
            raise GLMError("请求超时")
        except requests.exceptions.ConnectionError:
            raise GLMError("网络连接错误")
        except requests.exceptions.RequestException as e:
            raise GLMError(f"请求失败: {str(e)}")
    
    def chat(self, 
             prompt: str, 
             system_prompt: Optional[str] = None,
             temperature: float = 0.7,
             max_tokens: Optional[int] = None,
             top_p: float = 0.9) -> str:
        """
        发送聊天请求
        
        Args:
            prompt (str): 用户输入的提示词
            system_prompt (Optional[str]): 系统提示词
            temperature (float): 温度参数，控制随机性 (0.0-1.0)
            max_tokens (Optional[int]): 最大生成token数
            top_p (float): top_p参数，控制多样性 (0.0-1.0)
            
        Returns:
            str: 模型生成的回复内容
            
        Raises:
            GLMError: API调用失败时抛出
        """
        # 构建消息列表
        messages = []
        
        if system_prompt:
            messages.append({
                "role": "system",
                "content": system_prompt
            })
        
        messages.append({
            "role": "user", 
            "content": prompt
        })
        
        # 构建请求参数
        request_params = {
            "temperature": temperature,
            "top_p": top_p
        }
        
        if max_tokens:
            request_params["max_tokens"] = max_tokens
        
        # 发送请求
        result = self._make_request(messages, **request_params)
        
        # 提取回复内容
        try:
            content = result["choices"][0]["message"]["content"]
            return content.strip()
        except (KeyError, IndexError) as e:
            raise GLMError(f"响应格式错误: {str(e)}")
    
    def simple_chat(self, prompt: str) -> str:
        """
        简单聊天接口，使用默认参数
        
        Args:
            prompt (str): 用户输入的提示词
            
        Returns:
            str: 模型生成的回复内容
        """
        return self.chat(prompt)
    
    def sentiment_analysis(self, text: str) -> str:
        """
        情感分析专用接口

        Args:
            text (str): 要分析的文本

        Returns:
            str: 情感分析结果 ('正面', '负面', '未知')
        """
        system_prompt = """你是一个专业的金融新闻情感分析助手。请分析给定新闻文本的市场情感倾向。

分析规则：
1. 只关注对股票市场可能产生的影响
2. 忽略已经发生的股价变动新闻
3. 重点关注业务表现、产品评价、政策影响等潜在市场因素
4. 正面：业绩增长、新产品发布、政策利好、合作协议、技术突破等
5. 负面：业绩下滑、成本上升、政策不利、合规问题、竞争加剧等
6. 中性：模糊、模棱两可、不确定性强、影响不明确等
7. 未知：无法判断或信息不足

重要：请严格按照要求，只能回答以下四种结果之一：'正面'、'负面'、'中性'、'未知'
不要返回任何解释或其他内容"""

        prompt = f"请分析以下新闻的市场情感倾向：\n\n{text}\n\n请只回答：正面、负面、中性、或未知"

        max_attempts = 2
        for attempt in range(max_attempts):
            try:
                result = self.chat(prompt, system_prompt, temperature=0.2)

                # 处理GLM-Z1-Flash的<think>标签，提取实际答案
                result = result.strip()

                # 如果包含<think>标签，提取</think>后面的内容
                if '</think>' in result:
                    # 找到</think>标签的位置，提取后面的内容
                    think_end = result.find('</think>')
                    if think_end != -1:
                        actual_answer = result[think_end + 8:].strip()  # 8是</think>的长度
                    else:
                        actual_answer = result
                else:
                    actual_answer = result

                print(f"GLM情感分析原始响应长度: {len(result)}")
                print(f"GLM情感分析实际答案: '{actual_answer}'")

                # 精确匹配实际答案
                if actual_answer == '正面' or actual_answer == '正面':
                    return '正面'
                elif actual_answer == '负面' or actual_answer == '负面':
                    return '负面'
                elif actual_answer == '中性' or actual_answer == '中性':
                    return '中性'
                elif actual_answer == '未知' or actual_answer == '未知':
                    return '未知'
                elif '正面' in actual_answer and len(actual_answer) < 15:  # 简短回答中包含正面
                    return '正面'
                elif '负面' in actual_answer and len(actual_answer) < 15:  # 简短回答中包含负面
                    return '负面'
                elif '中性' in actual_answer and len(actual_answer) < 15:  # 简短回答中包含中性
                    return '中性'
                elif '未知' in actual_answer and len(actual_answer) < 15:  # 简短回答中包含未知
                    return '未知'
                else:
                    # 如果格式不正确，重试一次
                    if attempt < max_attempts - 1:
                        prompt = f"请重新分析以下新闻，严格按照要求只回答'正面'、'负面'、'中性'或'未知'中的一个：\n\n{text}"
                        continue
                    else:
                        print("情感分析匹配失败，默认返回'未知'")
                        return '未知'

            except GLMError:
                if attempt < max_attempts - 1:
                    continue
                else:
                    print("情感分析API调用失败，默认返回'未知'")
                    return '未知'

        return '未知'  # 默认返回'未知'
    
    def test_connection(self) -> bool:
        """
        测试API连接是否正常
        
        Returns:
            bool: 连接是否成功
        """
        try:
            result = self.simple_chat("你好")
            return bool(result)
        except GLMError:
            return False


# 便捷函数
def create_glm_client(api_key: str) -> GLMZ1Client:
    """
    创建GLM-Z1-Flash客户端的便捷函数
    
    Args:
        api_key (str): API密钥
        
    Returns:
        GLMZ1Client: 客户端实例
    """
    return GLMZ1Client(api_key)


# 使用示例
if __name__ == "__main__":
    # 示例用法
    api_key = "696316314d42490c818e3e5ded184701.xhgEGx1ZbrAsBFy7"
    client = create_glm_client(api_key)
    
    # 测试连接
    if client.test_connection():
        print("✓ API连接正常")
        
        # 简单聊天
        response = client.simple_chat("你好，请介绍一下GLM-Z1-Flash模型")
        print(f"回复: {response}")
        
        # 情感分析示例
        news_text = "某公司发布了新产品，预计将提升公司业绩"
        sentiment = client.sentiment_analysis(news_text)
        print(f"情感分析结果: {sentiment}")
    else:
        print("✗ API连接失败")
