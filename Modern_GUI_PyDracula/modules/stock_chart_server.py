#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票K线图表系统 - 整合服务器
同时提供HTTP服务器和WebSocket股票数据服务器
"""

import asyncio
import websockets
import json
import time
import logging
import threading
import http.server
import socketserver
import os
import webbrowser
from datetime import datetime
from pytdx.hq import TdxHq_API

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class StockDataServer:
    def __init__(self, websocket_host='localhost', websocket_port=8765):
        self.websocket_host = websocket_host
        self.websocket_port = websocket_port
        self.clients = set()  # 连接的客户端
        self.subscriptions = {}  # 订阅信息 {symbol: [client1, client2, ...]}
        
        # 初始化API，启用多线程和自动重连
        try:
            self.api = TdxHq_API(
                multithread=True,      # 启用多线程支持
                heartbeat=True,        # 启用心跳包
                auto_retry=True,       # 启用自动重试
                raise_exception=False  # 不抛出异常，返回None
            )
        except Exception as e:
            logger.warning(f"使用高级参数初始化API失败: {e}，使用基本参数")
            self.api = TdxHq_API()
            
        self.is_connected = False
        self.last_connect_time = 0  # 上次连接时间
        
        # 股票代码缓存 (动态添加)
        self.stock_codes = {}
        
        # K线数据缓存
        self.kline_cache = {}  # {symbol_period: data}

        # 实时数据管理
        self.realtime_subscriptions = {}  # {symbol_period: [websocket1, websocket2, ...]}
        self.realtime_task = None  # 实时数据任务
        self.is_realtime_running = False

    def connect_tdx(self):
        """连接通达信服务器"""
        try:
            # 优先使用测试过的可用服务器
            servers = [
                ('**************', 7709),  # 上海电信主站Z1 (最快)
                ('**************', 80),    # 上海电信主站Z80 (最快)
                ('************', 7709),    # 上证云成都电信一
                ('**************', 7709),  # 上证云北京联通一
            ]
            
            # 添加更多备用服务器
            try:
                from pytdx.config.hosts import hq_hosts
                # 添加其他服务器作为备用
                backup_servers = [(host[1], host[2]) for host in hq_hosts[4:15]]
                servers.extend(backup_servers)
            except ImportError:
                # 如果导入失败，使用更多备用服务器
                backup_servers = [
                    ('*************', 7709),   # 长城国瑞电信1
                    ('*************', 7709),   # 长城国瑞电信2
                    ('*************', 7709),   # 长城国瑞网通
                    ('**************', 7709),  # 上海电信主站Z2
                    ('***************', 7709), # 北京联通主站Z1
                    ('***************', 7709), # 北京联通主站Z2
                    ('**************', 7709),  # 备用服务器1
                    ('************', 7709),    # 备用服务器2
                    ('*************', 7709),   # 备用服务器3
                ]
                servers.extend(backup_servers)
            
            logger.info(f"尝试连接 {len(servers)} 个通达信服务器...")
            
            for i, (host, port) in enumerate(servers):
                try:
                    logger.info(f"正在尝试连接服务器 {i+1}/{len(servers)}: {host}:{port}")
                    
                    # 设置连接超时
                    if self.api.connect(host, port, time_out=5):
                        self.is_connected = True
                        logger.info(f"成功连接到通达信服务器: {host}:{port}")
                        
                        # 测试连接是否正常工作
                        try:
                            test_data = self.api.get_security_count(0)  # 测试获取深圳市场股票数量
                            if test_data and test_data > 0:
                                logger.info(f"连接测试成功，深圳市场股票数量: {test_data}")
                                return True
                            else:
                                logger.warning(f"连接测试失败，断开连接: {host}:{port}")
                                self.api.disconnect()
                                self.is_connected = False
                        except Exception as test_e:
                            logger.warning(f"连接测试异常: {test_e}")
                            self.api.disconnect()
                            self.is_connected = False
                            
                except Exception as e:
                    logger.warning(f"连接服务器 {host}:{port} 失败: {e}")
                    continue
            
            logger.error("无法连接到任何通达信服务器")
            return False
        except Exception as e:
            logger.error(f"连接通达信服务器异常: {e}")
            return False
    
    def ensure_connection(self):
        """确保连接可用，如果断开则重连"""
        current_time = time.time()
        
        # 如果未连接或距离上次连接超过5分钟，尝试重连
        if not self.is_connected or (current_time - self.last_connect_time) > 300:
            logger.info("检查连接状态...")
            
            # 测试当前连接
            if self.is_connected:
                try:
                    test_result = self.api.get_security_count(0)
                    if test_result and test_result > 0:
                        self.last_connect_time = current_time
                        return True
                    else:
                        logger.warning("连接测试失败，需要重连")
                        self.is_connected = False
                except Exception as e:
                    logger.warning(f"连接测试异常: {e}，需要重连")
                    self.is_connected = False
            
            # 重新连接
            if self.connect_tdx():
                self.last_connect_time = current_time
                return True
            else:
                return False
        
        return True

    def parse_stock_code(self, symbol: str):
        """
        解析股票代码，自动判断市场
        返回: {'code': 股票代码, 'market': 市场代码, 'name': 股票名称}
        """
        # 移除可能的前缀
        clean_symbol = symbol.upper()
        if ':' in clean_symbol:
            clean_symbol = clean_symbol.split(':')[-1]

        # 判断市场
        if clean_symbol.startswith('6'):
            # 上海市场：6开头
            market = 1
            exchange = 'SSE'
        elif clean_symbol.startswith(('0', '3')):
            # 深圳市场：0开头（主板、中小板）、3开头（创业板）
            market = 0
            exchange = 'SZSE'
        elif clean_symbol.startswith('8') or clean_symbol.startswith('4'):
            # 北交所：8开头（精选层）、4开头（创新层）
            market = 0  # 暂时归类到深圳市场
            exchange = 'BSE'
        else:
            # 默认深圳市场
            market = 0
            exchange = 'SZSE'

        # 尝试获取股票名称（可选）
        stock_name = f"股票 {clean_symbol}"
        try:
            # 这里可以添加从pytdx获取股票名称的逻辑
            # 暂时使用默认名称
            pass
        except Exception as e:
            logger.debug(f"获取股票名称失败: {e}")

        result = {
            'code': clean_symbol,
            'market': market,
            'name': stock_name,
            'exchange': exchange
        }

        # 缓存结果
        self.stock_codes[clean_symbol] = result
        logger.info(f"解析股票代码: {symbol} -> {result}")

        return result

    def calculate_close_average(self, bars):
        """
        计算所有K线close收盘价的算术平均值
        每增加一个close数据，重新计算所有close的平均值
        """
        if not bars or len(bars) == 0:
            return []

        try:
            # 计算收盘价平均值
            close_averages = []

            for i in range(len(bars)):
                # 计算从第一条到当前位置的所有收盘价平均值
                total_close = 0
                count = i + 1  # 当前位置包含的数据条数

                for j in range(count):
                    close_price = bars[j]['close']
                    total_close += close_price

                # 计算算术平均值
                close_avg = total_close / count
                close_averages.append(close_avg)

            logger.info(f"计算收盘价平均值完成，数据点数量: {len(close_averages)}")
            return close_averages

        except Exception as e:
            logger.error(f"计算收盘价平均值失败: {e}")
            return []

    def get_kline_data(self, symbol: str, period: int = 9, count: int = 800, start_pos: int = 0):
        """
        获取K线数据
        period: 0 5分钟K线 1 15分钟K线 2 30分钟K线 3 1小时K线 4 日K线
                5 周K线 6 月K线 7 1分钟 8 1分钟K线 9 日K线 10 季K线 11 年K线
        count: 获取数据的数量
        start_pos: 开始位置，0表示最新数据，数值越大表示越早的数据

        对于1分钟数据，支持获取更大数量的历史数据
        """
        if not self.ensure_connection():
            logger.error("无法建立连接，获取K线数据失败")
            return None
        
        try:
            # 获取或解析股票信息
            if symbol not in self.stock_codes:
                stock_info = self.parse_stock_code(symbol)
                if not stock_info:
                    logger.error(f"无法解析股票代码: {symbol}")
                    return None
            else:
                stock_info = self.stock_codes[symbol]

            market = stock_info['market']
            clean_symbol = stock_info['code']
            
            # 获取K线数据 - 对于大量数据，需要分批获取
            if count > 800:  # 数量较大时分批获取
                logger.info(f"获取大量数据: {count} 条，从位置 {start_pos} 开始，将分批获取")
                all_data = []
                batch_size = 800  # 每批最多800条
                current_pos = start_pos  # 使用传入的起始位置
                max_attempts = 50  # 最多尝试50批，防止无限循环
                attempt = 0

                while len(all_data) < count and attempt < max_attempts:
                    current_batch_size = min(batch_size, count - len(all_data))
                    logger.info(f"第{attempt + 1}批: 位置={current_pos}, 请求={current_batch_size}条")

                    batch_data = self.api.get_security_bars(period, market, clean_symbol, current_pos, current_batch_size)

                    if not batch_data:
                        logger.warning(f"第{attempt + 1}批数据获取失败，可能已到达历史数据边界")
                        break

                    # 检查是否有重复数据（时间戳相同）
                    new_data = []
                    existing_times = set()

                    # 先收集已有数据的时间戳
                    for existing_bar in all_data:
                        try:
                            existing_time = existing_bar.get('time') or existing_bar.get('datetime')
                            if existing_time:
                                existing_times.add(str(existing_time))
                        except Exception as e:
                            logger.warning(f"处理已有数据时间戳失败: {e}")

                    # 处理新批次数据
                    for bar in batch_data:
                        try:
                            bar_time = bar.get('time') or bar.get('datetime')
                            if bar_time and str(bar_time) not in existing_times:
                                new_data.append(bar)
                                existing_times.add(str(bar_time))
                        except Exception as e:
                            logger.warning(f"处理批次数据时间戳失败: {e}, 数据: {bar}")

                    if new_data:
                        all_data.extend(new_data)
                        logger.info(f"第{attempt + 1}批: 获取{len(batch_data)}条，去重后{len(new_data)}条，累计{len(all_data)}条")
                    else:
                        logger.info(f"第{attempt + 1}批: 无新数据，可能已到达历史数据边界")
                        break

                    current_pos += batch_size
                    attempt += 1

                    # 如果返回的数据少于请求的数量，说明没有更多数据了
                    if len(batch_data) < current_batch_size:
                        logger.info("已获取所有可用历史数据")
                        break

                data = all_data
                logger.info(f"分批获取完成，总计 {len(data)} 条数据，尝试了 {attempt + 1} 批")
            else:
                # 普通获取
                logger.info(f"普通获取: {count} 条数据，从位置 {start_pos} 开始")
                data = self.api.get_security_bars(period, market, clean_symbol, start_pos, count)
            
            if not data:
                logger.warning(f"未获取到K线数据: {symbol}")
                return None
            
            # 转换为TradingView格式
            bars = []
            for i, bar in enumerate(data):
                # 处理时间戳 - 根据调试结果，pytdx返回的是字符串格式
                try:
                    # 检查数据结构
                    if not isinstance(bar, dict):
                        logger.warning(f"第{i+1}条数据不是字典格式: {type(bar)} - {bar}")
                        continue

                    # 检查必需字段
                    required_fields = ['datetime', 'open', 'high', 'low', 'close', 'vol']
                    missing_fields = [field for field in required_fields if field not in bar]
                    if missing_fields:
                        logger.warning(f"第{i+1}条数据缺少字段 {missing_fields}: {bar}")
                        continue

                    from datetime import datetime

                    datetime_value = bar['datetime']
                    if isinstance(datetime_value, str):
                        # pytdx返回格式: '2025-06-18 14:58' 或 '2025-06-18 15:00'
                        dt = datetime.strptime(datetime_value, '%Y-%m-%d %H:%M')
                        # TradingView需要秒级时间戳，不是毫秒
                        timestamp = int(dt.timestamp())

                    elif hasattr(datetime_value, 'timestamp'):
                        # 如果是datetime对象
                        timestamp = int(datetime_value.timestamp())
                    else:
                        # 备用方案：使用当前时间
                        timestamp = int(time.time())
                        logger.warning(f"第{i+1}条数据时间格式未知: {type(datetime_value)}, 值: {datetime_value}")

                except Exception as time_e:
                    logger.warning(f"第{i+1}条数据时间戳转换失败: {time_e}, 原始数据: {bar}")
                    continue  # 跳过有问题的数据，而不是使用当前时间

                # 处理价格和成交量数据
                try:
                    bar_data = {
                        'time': timestamp,
                        'open': float(bar['open']),
                        'high': float(bar['high']),
                        'low': float(bar['low']),
                        'close': float(bar['close']),
                        'volume': int(bar['vol'])
                    }
                    bars.append(bar_data)
                except Exception as data_e:
                    logger.warning(f"第{i+1}条数据价格转换失败: {data_e}, 原始数据: {bar}")
                    continue

                # 调试：打印前3条和后3条数据的时间戳
                if len(bars) <= 3 or len(data) - len(bars) < 3:
                    from datetime import datetime
                    dt_str = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                    logger.info(f"调试K线数据 #{len(bars)}: 原始时间='{bar['datetime']}' -> 时间戳={timestamp} -> 格式化={dt_str}")

            # 计算收盘价平均值指标
            close_averages = self.calculate_close_average(bars)

            # 将指标值添加到每个K线数据中
            for i, bar in enumerate(bars):
                if i < len(close_averages):
                    bar['close_average'] = round(close_averages[i], 4)  # 保留4位小数
                else:
                    bar['close_average'] = bar['close']  # 备用值

            logger.info(f"成功获取K线数据: {symbol} 周期={period} 数量={len(bars)}，已添加收盘价平均值指标")
            return bars
            
        except Exception as e:
            logger.error(f"获取K线数据异常: {e}")
            return None

    def get_latest_kline_data(self, symbol: str, period: int = 7, count: int = 1):
        """
        获取最新的K线数据（用于实时更新）
        只获取最新的几条数据，用于增量更新
        """
        try:
            if not self.ensure_connection():
                logger.error("无法建立连接，获取最新K线数据失败")
                return None

            # 获取或解析股票信息
            if symbol not in self.stock_codes:
                stock_info = self.parse_stock_code(symbol)
                if not stock_info:
                    logger.error(f"无法解析股票代码: {symbol}")
                    return None
            else:
                stock_info = self.stock_codes[symbol]

            market = stock_info['market']
            clean_symbol = stock_info['code']

            # 获取最新的K线数据
            data = self.api.get_security_bars(period, market, clean_symbol, 0, count)

            if not data:
                return None

            # 转换为TradingView格式
            bars = []
            for bar in data:
                try:
                    # 检查必需字段
                    required_fields = ['datetime', 'open', 'high', 'low', 'close', 'vol']
                    missing_fields = [field for field in required_fields if field not in bar]
                    if missing_fields:
                        continue

                    from datetime import datetime

                    datetime_value = bar['datetime']
                    if isinstance(datetime_value, str):
                        dt = datetime.strptime(datetime_value, '%Y-%m-%d %H:%M')
                        timestamp = int(dt.timestamp())
                    elif hasattr(datetime_value, 'timestamp'):
                        timestamp = int(datetime_value.timestamp())
                    else:
                        timestamp = int(time.time())

                    logger.debug(f"实时数据转换: {datetime_value} -> {timestamp} ({datetime.fromtimestamp(timestamp)})")

                    bar_data = {
                        'time': timestamp,
                        'open': float(bar['open']),
                        'high': float(bar['high']),
                        'low': float(bar['low']),
                        'close': float(bar['close']),
                        'volume': int(bar['vol'])
                    }
                    bars.append(bar_data)

                except Exception as e:
                    logger.warning(f"处理最新K线数据失败: {e}")
                    continue

            # 为实时数据也计算收盘价平均值指标
            # 注意：实时数据通常只有1条，所以平均值就是当前收盘价
            if bars:
                for bar in bars:
                    bar['close_average'] = bar['close']  # 实时数据的平均值等于收盘价
                logger.info(f"实时数据已添加收盘价平均值指标，数量: {len(bars)}")

            return bars

        except Exception as e:
            logger.error(f"获取最新K线数据异常: {e}")
            return None

    async def realtime_data_task(self):
        """实时数据推送任务"""
        logger.info("启动实时数据推送任务")

        while self.is_realtime_running:
            try:
                # 检查是否有订阅
                if not self.realtime_subscriptions:
                    await asyncio.sleep(5)  # 没有订阅时等待5秒
                    continue

                # 遍历所有订阅
                for symbol_period, websockets in list(self.realtime_subscriptions.items()):
                    if not websockets:  # 如果没有客户端订阅，跳过
                        continue

                    try:
                        # 解析symbol和period
                        parts = symbol_period.split('_')
                        if len(parts) != 2:
                            continue

                        symbol = parts[0]
                        period = int(parts[1])

                        # 获取最新数据
                        latest_data = self.get_latest_kline_data(symbol, period, 1)

                        if latest_data and len(latest_data) > 0:
                            latest_bar = latest_data[0]
                            logger.info(f"获取到实时数据: {symbol} 时间={latest_bar['time']} 价格={latest_bar['close']}")

                            # 准备实时数据消息
                            message = {
                                'type': 'realtime_update',
                                'symbol': symbol,
                                'period': period,
                                'data': latest_bar  # 只发送最新的一条数据
                            }

                            # 发送给所有订阅的客户端
                            disconnected_clients = []
                            for ws in websockets:
                                try:
                                    await ws.send(json.dumps(message))
                                    logger.debug(f"实时数据已发送到客户端: {symbol}")
                                except websockets.exceptions.ConnectionClosed:
                                    disconnected_clients.append(ws)
                                except Exception as e:
                                    logger.warning(f"发送实时数据失败: {e}")
                                    disconnected_clients.append(ws)

                            # 清理断开的连接
                            for ws in disconnected_clients:
                                if ws in websockets:
                                    websockets.remove(ws)

                            # 如果没有客户端了，删除订阅
                            if not websockets:
                                del self.realtime_subscriptions[symbol_period]
                        else:
                            logger.debug(f"未获取到实时数据: {symbol_period}")

                    except Exception as e:
                        logger.warning(f"处理实时数据推送失败 {symbol_period}: {e}")
                        # 如果连续出错，暂停一下
                        await asyncio.sleep(5)

                # 等待一段时间再获取下一次数据
                await asyncio.sleep(30)  # 每30秒更新一次，减少服务器压力

            except Exception as e:
                logger.error(f"实时数据任务异常: {e}")
                await asyncio.sleep(5)

        logger.info("实时数据推送任务已停止")

    def start_realtime_task(self):
        """启动实时数据任务"""
        if not self.is_realtime_running:
            self.is_realtime_running = True
            self.realtime_task = asyncio.create_task(self.realtime_data_task())
            logger.info("实时数据任务已启动")

    def stop_realtime_task(self):
        """停止实时数据任务"""
        if self.is_realtime_running:
            self.is_realtime_running = False
            if self.realtime_task:
                self.realtime_task.cancel()
            logger.info("实时数据任务已停止")

    async def handle_message(self, websocket, message):
        """处理WebSocket消息"""
        try:
            data = json.loads(message)
            action = data.get('action')
            
            if action == 'subscribe':
                # 订阅股票数据
                symbol = data.get('symbol')
                if symbol not in self.subscriptions:
                    self.subscriptions[symbol] = []
                if websocket not in self.subscriptions[symbol]:
                    self.subscriptions[symbol].append(websocket)
                
                logger.info(f"客户端订阅股票: {symbol}")
                
                # 发送历史K线数据
                kline_data = self.get_kline_data(symbol, 9, 800)  # 默认日K线
                response = {
                    'type': 'kline_data',
                    'symbol': symbol,
                    'data': kline_data or []
                }
                await websocket.send(json.dumps(response))
                
            elif action == 'unsubscribe':
                # 取消订阅
                symbol = data.get('symbol')
                if symbol in self.subscriptions and websocket in self.subscriptions[symbol]:
                    self.subscriptions[symbol].remove(websocket)
                    if not self.subscriptions[symbol]:
                        del self.subscriptions[symbol]
                
                logger.info(f"客户端取消订阅股票: {symbol}")
                
            elif action == 'get_kline':
                # 获取K线数据
                symbol = data.get('symbol')
                period = data.get('period', 9)  # 默认日K线
                count = data.get('count', 800)
                start_pos = data.get('start_pos', 0)  # 新增起始位置参数

                # 记录不同周期的数据请求
                period_names = {
                    0: "5分钟", 1: "15分钟", 2: "30分钟", 3: "1小时",
                    4: "日线", 5: "周线", 6: "月线", 7: "1分钟"
                }
                period_name = period_names.get(period, f"周期{period}")
                logger.info(f"请求{period_name}K线数据: {symbol}, 数量: {count}, 起始位置: {start_pos}")

                kline_data = self.get_kline_data(symbol, period, count, start_pos)

                # 调试：打印发送的数据样本
                if kline_data and len(kline_data) > 0:
                    from datetime import datetime
                    first_bar = kline_data[0]
                    last_bar = kline_data[-1]
                    first_dt = datetime.fromtimestamp(first_bar['time']).strftime('%Y-%m-%d %H:%M:%S')
                    last_dt = datetime.fromtimestamp(last_bar['time']).strftime('%Y-%m-%d %H:%M:%S')
                    logger.info(f"调试发送数据: 第一条时间={first_bar['time']} ({first_dt}), 最后一条时间={last_bar['time']} ({last_dt})")

                response = {
                    'type': 'kline_response',
                    'symbol': symbol,
                    'period': period,
                    'data': kline_data or []
                }
                await websocket.send(json.dumps(response))

                logger.info(f"发送K线数据响应: {symbol}, 周期: {period}, 数量: {len(kline_data) if kline_data else 0}")

            elif action == 'subscribe_realtime':
                # 订阅实时数据
                symbol = data.get('symbol')
                period = data.get('period', 7)

                if symbol:
                    symbol_period = f"{symbol}_{period}"

                    # 添加到订阅列表
                    if symbol_period not in self.realtime_subscriptions:
                        self.realtime_subscriptions[symbol_period] = []

                    if websocket not in self.realtime_subscriptions[symbol_period]:
                        self.realtime_subscriptions[symbol_period].append(websocket)
                        logger.info(f"客户端订阅实时数据: {symbol_period}")

                    # 启动实时数据任务（如果还没启动）
                    if not self.is_realtime_running:
                        self.start_realtime_task()

                    response = {
                        'type': 'subscribe_response',
                        'symbol': symbol,
                        'period': period,
                        'status': 'success'
                    }
                else:
                    response = {
                        'type': 'subscribe_response',
                        'status': 'error',
                        'message': '缺少symbol参数'
                    }

                await websocket.send(json.dumps(response))

            elif action == 'unsubscribe_realtime':
                # 取消订阅实时数据
                symbol = data.get('symbol')
                period = data.get('period', 7)

                if symbol:
                    symbol_period = f"{symbol}_{period}"

                    if symbol_period in self.realtime_subscriptions:
                        if websocket in self.realtime_subscriptions[symbol_period]:
                            self.realtime_subscriptions[symbol_period].remove(websocket)
                            logger.info(f"客户端取消订阅实时数据: {symbol_period}")

                        # 如果没有客户端了，删除订阅
                        if not self.realtime_subscriptions[symbol_period]:
                            del self.realtime_subscriptions[symbol_period]

                    response = {
                        'type': 'unsubscribe_response',
                        'symbol': symbol,
                        'period': period,
                        'status': 'success'
                    }
                else:
                    response = {
                        'type': 'unsubscribe_response',
                        'status': 'error',
                        'message': '缺少symbol参数'
                    }

                await websocket.send(json.dumps(response))

            elif action == 'search_symbols':
                # 搜索股票代码
                query = data.get('query', '')

                # 简单的搜索实现
                results = []
                if query and len(query) >= 1:
                    query_upper = query.upper()

                    # 如果是6位数字，直接返回对应的股票
                    if query_upper.isdigit() and len(query_upper) == 6:
                        stock_info = self.parse_stock_code(query_upper)
                        results.append({
                            'symbol': f"{stock_info['exchange']}:{stock_info['code']}",
                            'full_name': f"{stock_info['exchange']}:{stock_info['code']}",
                            'description': stock_info['name'],
                            'exchange': stock_info['exchange'],
                            'ticker': stock_info['code'],
                            'type': 'stock'
                        })

                response = {
                    'type': 'search_response',
                    'query': query,
                    'results': results
                }

                await websocket.send(json.dumps(response))

        except Exception as e:
            logger.error(f"处理WebSocket消息异常: {e}")

    async def register_client(self, websocket, path):
        """注册新客户端并处理消息"""
        self.clients.add(websocket)
        logger.info(f"客户端连接: {websocket.remote_address}")
        
        try:
            # 监听客户端消息
            async for message in websocket:
                await self.handle_message(websocket, message)
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"客户端正常断开: {websocket.remote_address}")
        except Exception as e:
            logger.error(f"客户端连接异常: {e}")
        finally:
            # 清理客户端
            if websocket in self.clients:
                self.clients.remove(websocket)
            
            # 清理订阅
            for symbol in list(self.subscriptions.keys()):
                if websocket in self.subscriptions[symbol]:
                    self.subscriptions[symbol].remove(websocket)
                    if not self.subscriptions[symbol]:
                        del self.subscriptions[symbol]

            # 清理实时数据订阅
            for symbol_period in list(self.realtime_subscriptions.keys()):
                if websocket in self.realtime_subscriptions[symbol_period]:
                    self.realtime_subscriptions[symbol_period].remove(websocket)
                    if not self.realtime_subscriptions[symbol_period]:
                        del self.realtime_subscriptions[symbol_period]

            logger.info(f"客户端断开连接清理完成: {websocket.remote_address}")

    async def start_websocket_server(self):
        """启动WebSocket服务器"""
        logger.info(f"启动股票数据WebSocket服务器: ws://{self.websocket_host}:{self.websocket_port}")
        
        # 连接通达信服务器
        if not self.connect_tdx():
            logger.error("无法连接通达信服务器，WebSocket服务器启动失败")
            return
        
        # 启动WebSocket服务器
        async def handler(websocket):
            await self.register_client(websocket, None)
        
        async with websockets.serve(handler, self.websocket_host, self.websocket_port):
            logger.info("股票数据WebSocket服务器已启动")
            await asyncio.Future()  # 保持运行


class HTTPServer:
    def __init__(self, port=8080):
        self.port = port
        
    def start_http_server(self):
        """启动HTTP服务器"""
        try:
            # 切换到项目根目录
            os.chdir(os.path.dirname(os.path.abspath(__file__)))
            
            Handler = http.server.SimpleHTTPRequestHandler
            
            # 添加CORS头部支持
            class CORSRequestHandler(Handler):
                def end_headers(self):
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', '*')
                    super().end_headers()
            
            with socketserver.TCPServer(("", self.port), CORSRequestHandler) as httpd:
                logger.info(f"🚀 HTTP服务器启动成功: http://localhost:{self.port}")
                logger.info(f"📊 标准图表页面: http://localhost:{self.port}/stock-chart-standard.html")
                logger.info(f"🔧 完整图表页面: http://localhost:{self.port}/stock-chart.html")
                
                # 3秒后自动打开浏览器
                def open_browser():
                    time.sleep(3)
                    logger.info(f"🌐 正在打开浏览器...")
                    try:
                        import platform
                        url = f'http://localhost:{self.port}/stock-chart-standard.html'

                        # 根据操作系统选择不同的打开方式
                        system = platform.system().lower()
                        if system == 'windows':
                            import subprocess
                            subprocess.run(['start', url], shell=True, check=False)
                            logger.info(f"✅ 已使用Windows默认浏览器打开: {url}")
                        elif system == 'darwin':  # macOS
                            import subprocess
                            subprocess.run(['open', url], check=False)
                            logger.info(f"✅ 已使用macOS默认浏览器打开: {url}")
                        elif system == 'linux':
                            import subprocess
                            subprocess.run(['xdg-open', url], check=False)
                            logger.info(f"✅ 已使用Linux默认浏览器打开: {url}")
                        else:
                            # 备用方案：使用webbrowser模块
                            webbrowser.open(url)
                            logger.info(f"✅ 已使用webbrowser模块打开: {url}")

                    except Exception as e:
                        logger.warning(f"⚠️ 无法自动打开浏览器: {e}")
                        logger.info(f"💡 请手动在浏览器中访问: http://localhost:{self.port}/stock-chart-standard.html")

                browser_thread = threading.Thread(target=open_browser)
                browser_thread.daemon = True
                browser_thread.start()
                
                httpd.serve_forever()
                
        except KeyboardInterrupt:
            logger.info("HTTP服务器已停止")
        except OSError as e:
            if "Address already in use" in str(e):
                logger.warning(f"端口 {self.port} 已被占用，尝试使用端口 {self.port + 1}")
                self.port += 1
                self.start_http_server()
            else:
                logger.error(f"启动HTTP服务器失败: {e}")


async def main():
    """主函数 - 同时启动HTTP服务器和WebSocket服务器"""
    logger.info("=" * 60)
    logger.info("🏦 股票K线图表系统 - 整合服务器启动中...")
    logger.info("=" * 60)
    
    # 检查必要文件是否存在
    required_files = [
        'charting_library/charting_library.js',
        'charting_library/custom_indicators/indicators_loader.js',
        'charting_library/custom_indicators/ma_cross.js'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        logger.error("❌ 缺少必要文件:")
        for file in missing_files:
            logger.error(f"   - {file}")
        logger.error("请确保所有文件都存在后再启动服务器")
        return
    
    logger.info("✅ 所有必要文件检查完成")
    
    # 创建服务器实例
    stock_server = StockDataServer()
    http_server = HTTPServer()
    
    # 在单独的线程中启动HTTP服务器
    http_thread = threading.Thread(target=http_server.start_http_server)
    http_thread.daemon = True
    http_thread.start()
    
    logger.info("💡 使用说明:")
    logger.info("1. HTTP服务器提供网页访问 (端口8080)")
    logger.info("2. WebSocket服务器提供股票数据 (端口8765)")
    logger.info("3. 在浏览器中访问图表页面")
    logger.info("4. 按 Ctrl+C 停止所有服务器")
    logger.info("")
    
    # 启动WebSocket服务器 (在主线程中运行)
    try:
        await stock_server.start_websocket_server()
    except KeyboardInterrupt:
        logger.info("✅ 所有服务器已停止")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序已退出")
