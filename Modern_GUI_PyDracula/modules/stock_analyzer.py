# ///////////////////////////////////////////////////////////////
#
# 股票分析模块
# 负责获取股票数据和计算筹码峰
#
# ///////////////////////////////////////////////////////////////

import pandas as pd
import numpy as np
import copy
from pytdx.hq import TdxHq_API
from datetime import datetime, timedelta
import matplotlib
# 尝试使用Qt5Agg后端，如果失败则使用Agg后端
try:
    matplotlib.use('Qt5Agg')
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    QT_AVAILABLE = True
except ImportError:
    matplotlib.use('Agg')  # 使用非交互式后端
    FigureCanvas = None
    QT_AVAILABLE = False
    print("⚠️ Qt后端不可用，使用Agg后端")

import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from matplotlib.patches import Rectangle

# CYQ筹码分布算法 - 需要换手率数据进行筹码衰减和分布计算
# 换手率在CYQ算法中的作用：
# 1. 筹码衰减：每天根据换手率让旧筹码衰减 xdata[n] *= (1 - turnover_rate)
# 2. 新筹码分布：根据当天换手率在相应价格区间增加新筹码
# 3. 换手率越高，筹码流动性越强，分布变化越大


class CYQCalculator:
    """
    筹码分布算法计算器 - 基于 /stock/instock/core/kline/cyq.py
    """
    def __init__(self, kdata, accuracy_factor=150, crange=120, cyq_days=210):
        # K图数据
        self.klinedata = kdata
        # 精度因子(纵轴刻度数)
        self.fator = accuracy_factor
        # 计算K线条数
        self.range = crange
        # 计算筹码分布的交易天数
        self.tradingdays = cyq_days

    def calc(self, index):
        """
        计算分布及相关指标

        Args:
            index: 当前选中的K线的索引

        Returns:
            CYQData: 包含筹码分布数据的对象
        """
        maxprice = 0
        minprice = 1000000
        factor = self.fator

        # 简化数据选择逻辑：直接使用最近的tradingdays天数据
        if len(self.klinedata) <= self.tradingdays:
            kdata = self.klinedata.copy()
        else:
            kdata = self.klinedata.tail(self.tradingdays).copy()

        print(f"CYQ计算使用数据: {len(kdata)} 天，日期范围: {kdata.iloc[0]['date'] if 'date' in kdata.columns else '未知'} 到 {kdata.iloc[-1]['date'] if 'date' in kdata.columns else '未知'}")

        # 计算价格范围
        for _high, _low in zip(kdata['high'].values, kdata['low'].values):
            maxprice = max(maxprice, _high)
            minprice = min(minprice, _low)

        # 精度不小于0.01产品逻辑
        accuracy = max(0.01, (maxprice - minprice) / (factor - 1))
        currentprice = kdata.iloc[-1]['close']
        boundary = -1

        # 值域
        yrange = []
        for i in range(factor):
            _price = float(f"{minprice + accuracy * i:.2f}")
            yrange.append(_price)
            if boundary == -1 and _price >= currentprice:
                boundary = i

        # 横轴数据
        xdata = [0] * factor

        # 计算筹码分布
        for open_price, close, high, low, turnover in zip(
            kdata['open'].values, kdata['close'].values,
            kdata['high'].values, kdata['low'].values,
            kdata['turnover'].values if 'turnover' in kdata.columns else kdata['turnover_rate'].values
        ):
            avg = (open_price + close + high + low) / 4
            turnover_rate = min(1, turnover / 100)

            H = int((high - minprice) / accuracy)
            L = int((low - minprice) / accuracy + 0.99)
            # G点坐标, 一字板时, X为进度因子
            GPoint = [factor - 1 if high == low else 2 / (high - low),
                      int((avg - minprice) / accuracy)]

            # 筹码衰减
            for n in range(len(xdata)):
                xdata[n] *= (1 - turnover_rate)

            if high == low:
                # 一字板时，画矩形面积是三角形的2倍
                xdata[GPoint[1]] += GPoint[0] * turnover_rate / 2
            else:
                for j in range(L, H + 1):
                    curprice = minprice + accuracy * j
                    if curprice <= avg:
                        # 上半三角叠加分布
                        if abs(avg - low) < 1e-8:
                            xdata[j] += GPoint[0] * turnover_rate
                        else:
                            xdata[j] += (curprice - low) / (avg - low) * GPoint[0] * turnover_rate
                    else:
                        # 下半三角叠加分布
                        if abs(high - avg) < 1e-8:
                            xdata[j] += GPoint[0] * turnover_rate
                        else:
                            xdata[j] += (high - curprice) / (high - avg) * GPoint[0] * turnover_rate

        total_chips = sum(float(f"{x:.12g}") for x in xdata)

        # 检查total_chips是否为0（兼容原版cyq.py逻辑）
        if total_chips == 0:
            print("⚠️ 总筹码为0，可能是换手率数据异常或股票交易天数不足")
            # 设置最小筹码值以避免除零错误
            total_chips = 1e-10

        print(f"总筹码量: {total_chips:.6f}")

        # 获取指定筹码处的成本
        def get_cost_by_chip(chip):
            result = 0
            sum_chips = 0
            for i in range(factor):
                x = float(f"{xdata[i]:.12g}")
                if sum_chips + x > chip:
                    result = minprice + i * accuracy
                    break
                sum_chips += x
            return result

        # 筹码分布数据类
        class CYQData:
            def __init__(self):
                # 筹码堆叠
                self.x = None
                # 价格分布
                self.y = None
                # 获利比例
                self.benefit_part = None
                # 平均成本
                self.avg_cost = None
                # 百分比筹码
                self.percent_chips = None
                # 筹码堆叠亏盈分界下标
                self.b = None
                # 交易日期
                self.d = None
                # 交易天数
                self.t = None

            # 计算指定百分比的筹码
            @staticmethod
            def compute_percent_chips(percent):
                if percent > 1 or percent < 0:
                    raise ValueError('argument "percent" out of range')
                ps = [(1 - percent) / 2, (1 + percent) / 2]
                pr = [get_cost_by_chip(total_chips * ps[0]),
                      get_cost_by_chip(total_chips * ps[1])]
                return {
                    'priceRange': [f"{pr[0]:.2f}", f"{pr[1]:.2f}"],
                    'concentration': 0 if pr[0] + pr[1] == 0 else (pr[1] - pr[0]) / (pr[0] + pr[1])
                }

            # 获取指定价格的获利比例 - 根据筹码分布计算
            @staticmethod
            def get_benefit_part(price):
                """
                根据筹码分布计算获利比例
                获利比例 = 成本低于当前价格的筹码数量 / 总筹码数量
                这是正确的筹码分布获利比例算法
                """
                if total_chips == 0:
                    return 0.0

                below = 0  # 获利筹码数量
                for i in range(factor):
                    x = float(f"{xdata[i]:.12g}")
                    # 如果当前价格大于等于该价格区间，则该区间的筹码为获利筹码
                    if price >= minprice + i * accuracy:
                        below += x

                return below / total_chips

        result = CYQData()
        result.x = xdata
        result.y = yrange
        result.b = boundary + 1
        result.d = kdata.iloc[-1]['date'] if 'date' in kdata.columns else len(kdata) - 1
        result.t = len(kdata)  # 使用实际使用的数据天数

        # 计算平均成本
        avg_cost_value = get_cost_by_chip(total_chips * 0.5)
        result.avg_cost = f"{avg_cost_value:.2f}"

        # 根据筹码分布计算获利比例
        result.benefit_part = result.get_benefit_part(currentprice)

        result.percent_chips = {
            '90': result.compute_percent_chips(0.9),
            '70': result.compute_percent_chips(0.7)
        }

        return result



# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False  # 正常显示负号

# 注意：已移除akshare依赖，因为CYQ算法已包含筹码分布计算

from typing import Dict, List, Tuple, Optional

# 删除了ChipDistributionAnalyzer类 - 只保留CYQ算法













# 删除了ChipDistribution类 - 只保留CYQ算法


class CYQChipAnalyzer:
    """基于 CYQ 算法的筹码分布分析器"""

    def __init__(self):
        """初始化 CYQ 筹码分布分析器"""
        print("✅ CYQChipAnalyzer 初始化成功")

    def analyze_stock_with_cyq(self, stock_data):
        """
        使用 CYQ 算法分析股票筹码分布

        Args:
            stock_data: 包含 OHLC 和换手率数据的 DataFrame

        Returns:
            dict: 包含筹码分布数据和统计指标的字典
        """
        try:
            print("=== 开始 CYQ 筹码分布分析 ===")

            # 确保数据格式正确
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in stock_data.columns:
                    raise ValueError(f"缺少必要的数据列: {col}")

            # 检查换手率列
            if 'turnover_rate' not in stock_data.columns and 'turnover' not in stock_data.columns:
                print("⚠️ 缺少换手率数据，使用智能默认值")
                # 根据成交量大小智能设置默认换手率
                avg_volume = stock_data['volume'].mean()
                if avg_volume > 100000:  # 大成交量股票
                    default_turnover = 8.0
                elif avg_volume > 50000:  # 中等成交量股票
                    default_turnover = 5.0
                else:  # 小成交量股票
                    default_turnover = 3.0

                stock_data['turnover_rate'] = default_turnover
                print(f"根据平均成交量 {avg_volume:.0f}手，设置默认换手率为 {default_turnover}%")
            else:
                # 检查换手率数据的有效性
                turnover_col = 'turnover' if 'turnover' in stock_data.columns else 'turnover_rate'
                turnover_data = stock_data[turnover_col]
                print(f"使用换手率列: {turnover_col}")
                print(f"换手率数据范围: {turnover_data.min():.4f} - {turnover_data.max():.4f}")
                print(f"换手率平均值: {turnover_data.mean():.4f}")

                # 检查是否有异常的换手率数据
                if turnover_data.max() == 0:
                    print("⚠️ 所有换手率数据为0，这可能导致筹码分布计算异常")
                elif turnover_data.min() < 0:
                    print("⚠️ 发现负数换手率，将被处理为0")

            print(f"数据行数: {len(stock_data)}")
            print(f"数据列: {list(stock_data.columns)}")

            # 创建 CYQ 计算器
            cyq_calculator = CYQCalculator(
                kdata=stock_data,
                accuracy_factor=150,  # 精度因子
                crange=120,          # 计算范围
                cyq_days=210         # 计算交易天数
            )

            # 计算筹码分布（使用最新数据）
            latest_index = len(stock_data) - 1
            cyq_result = cyq_calculator.calc(latest_index)

            print("✅ CYQ 筹码分布计算完成")

            # 提取统计指标
            current_price = stock_data.iloc[-1]['close']
            avg_cost = float(cyq_result.avg_cost)
            benefit_part = cyq_result.benefit_part

            # 90% 和 70% 筹码区间
            chips_90 = cyq_result.percent_chips['90']
            chips_70 = cyq_result.percent_chips['70']

            # 构建返回结果
            result = {
                'cyq_data': cyq_result,
                'current_price': current_price,
                'avg_cost': avg_cost,
                'benefit_part': benefit_part,
                'chips_90': chips_90,
                'chips_70': chips_70,
                'price_range': cyq_result.y,
                'chip_distribution': cyq_result.x,
                'trading_days': cyq_result.t
            }

            print(f"实际使用天数: {cyq_result.t}")
            print(f"当前价格: {current_price:.2f}")
            print(f"平均成本: {avg_cost:.2f}")
            print(f"获利比例: {benefit_part:.2%}")
            print(f"90%筹码区间: {chips_90['priceRange'][0]} - {chips_90['priceRange'][1]}")
            print(f"90%筹码集中度: {chips_90['concentration']:.4f}")
            print(f"70%筹码区间: {chips_70['priceRange'][0]} - {chips_70['priceRange'][1]}")
            print(f"70%筹码集中度: {chips_70['concentration']:.4f}")

            return result

        except Exception as e:
            print(f"❌ CYQ 筹码分布分析失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def create_cyq_chart(self, stock_name, cyq_analysis, stock_data):
        """
        创建包含K线图和筹码分布图的综合图表

        Args:
            stock_name: 股票名称
            cyq_analysis: CYQ分析结果
            stock_data: 股票K线数据

        Returns:
            matplotlib Figure 对象
        """
        try:
            print("=== 创建 CYQ 筹码分布图表 ===")

            if cyq_analysis is None:
                raise ValueError("CYQ分析结果为空")

            # 创建图表 - 左侧K线图，右侧筹码分布图
            fig = Figure(figsize=(18, 10), dpi=100)

            # 创建子图布局：左侧K线图，右侧筹码分布图
            ax_kline = fig.add_subplot(1, 2, 1)  # K线图
            ax_chip = fig.add_subplot(1, 2, 2)   # 筹码分布图

            # 绘制K线图（210天）
            self._plot_kline_chart(ax_kline, stock_data, stock_name)

            # 绘制筹码分布图
            self._plot_chip_distribution(ax_chip, cyq_analysis, stock_name)

            # 调整布局
            fig.tight_layout()

            print("✅ CYQ 筹码分布图表创建完成")
            return fig

        except Exception as e:
            print(f"❌ 创建 CYQ 图表失败: {e}")
            import traceback
            traceback.print_exc()

            # 创建错误图表
            fig = Figure(figsize=(10, 6))
            ax = fig.add_subplot(111)
            ax.text(0.5, 0.5, f'CYQ 筹码分布图创建失败\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center', fontsize=12)
            ax.set_title(f"CYQ 筹码分布图 - {stock_name} (错误)", fontsize=14)
            return fig

    def _plot_kline_chart(self, ax, stock_data, stock_name):
        """绘制K线图（蜡烛图）"""
        try:
            # 使用所有历史数据
            plot_data = stock_data.copy()

            # 如果数据量太大（超过2000条），进行采样以提高显示性能
            if len(plot_data) > 2000:
                print(f"数据量较大({len(plot_data)}条)，进行采样显示最近2000条")
                plot_data = plot_data.tail(2000)

            print(f"K线图显示数据量: {len(plot_data)} 条")

            # 创建日期索引
            dates = range(len(plot_data))

            # 绘制蜡烛图
            for i, (idx, row) in enumerate(plot_data.iterrows()):
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']

                # 确定颜色：涨为红色，跌为绿色
                if close_price >= open_price:
                    color = 'red'      # 涨
                    body_color = 'red'
                else:
                    color = 'green'    # 跌
                    body_color = 'green'

                # 绘制影线（最高价到最低价的竖线）
                ax.plot([i, i], [low_price, high_price], color=color, linewidth=0.8, alpha=0.8)

                # 绘制实体（开盘价到收盘价的矩形）
                body_height = abs(close_price - open_price)
                body_bottom = min(open_price, close_price)

                if body_height > 0:
                    # 有实体的K线
                    rect = Rectangle((i-0.4, body_bottom), 0.8, body_height,
                                   facecolor=body_color, edgecolor=color, alpha=0.8, linewidth=0.5)
                    ax.add_patch(rect)
                else:
                    # 十字星（开盘价等于收盘价）
                    ax.plot([i-0.4, i+0.4], [close_price, close_price], color=color, linewidth=1)

            # 设置图表属性
            ax.set_title(f'K线图 ({len(plot_data)}天)', fontsize=11, pad=8)  # 小标题，减少间距
            ax.set_xlabel('交易日', fontsize=12)
            ax.set_ylabel('价格 (元)', fontsize=12)
            ax.grid(True, alpha=0.3)

            # 设置x轴标签（显示部分日期）
            step = max(1, len(dates) // 10)
            ax.set_xticks(dates[::step])
            ax.set_xticklabels([f'T-{len(dates)-i}' for i in dates[::step]], rotation=45)

            # 设置y轴范围，留出一些边距
            price_min = plot_data['low'].min()
            price_max = plot_data['high'].max()
            price_range = price_max - price_min
            ax.set_ylim(price_min - price_range * 0.05, price_max + price_range * 0.05)

            # 优化Y轴刻度显示，确保价格清晰可见
            import matplotlib.ticker as ticker

            # 统一设置Y轴刻度格式为一位小数
            ax.yaxis.set_major_formatter(ticker.FormatStrFormatter('%.1f'))

            # 设置Y轴刻度数量，避免过于密集
            ax.yaxis.set_major_locator(ticker.MaxNLocator(nbins=8, prune='both'))

            # 缩小Y轴标签的字体大小，确保能容纳4位数
            ax.tick_params(axis='y', labelsize=6)

        except Exception as e:
            print(f"❌ 绘制K线图失败: {e}")
            ax.text(0.5, 0.5, f'K线图绘制失败\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def _plot_chip_distribution(self, ax, cyq_analysis, stock_name):
        """绘制筹码分布图"""
        try:
            price_range = cyq_analysis['price_range']
            chip_distribution = cyq_analysis['chip_distribution']
            current_price = cyq_analysis['current_price']

            # 过滤掉筹码量很小的数据点（降低阈值，提高兼容性）
            filtered_data = [(p, c) for p, c in zip(price_range, chip_distribution) if c > 1e-6]

            # 如果过滤后没有数据，使用原始数据（兼容原版cyq.py逻辑）
            if not filtered_data:
                print("⚠️ 过滤后无有效筹码数据，使用原始数据")
                # 使用所有非零数据
                filtered_data = [(p, c) for p, c in zip(price_range, chip_distribution) if c > 0]

                # 如果仍然没有数据，使用所有数据（完全兼容原版）
                if not filtered_data:
                    print("⚠️ 无非零筹码数据，使用全部数据")
                    filtered_data = list(zip(price_range, chip_distribution))

                # 最后检查
                if not filtered_data:
                    raise ValueError("没有有效的筹码分布数据")

            prices, chips = zip(*filtered_data)
            print(f"筹码分布数据点数量: {len(filtered_data)}, 筹码值范围: {min(chips):.6f} - {max(chips):.6f}")

            # 根据当前价格分别绘制获利盘和套牢盘
            profit_prices = []  # 获利盘价格（当前价格以下）
            profit_chips = []   # 获利盘筹码
            loss_prices = []    # 套牢盘价格（当前价格以上）
            loss_chips = []     # 套牢盘筹码

            for price, chip in zip(prices, chips):
                if price <= current_price:
                    profit_prices.append(price)
                    profit_chips.append(chip)
                else:
                    loss_prices.append(price)
                    loss_chips.append(chip)

            # 绘制获利盘（当前价格以下，绿色）
            if profit_prices:
                ax.barh(profit_prices, profit_chips, height=0.01, alpha=0.7, color='lightgreen', edgecolor='green', label='获利盘')

            # 绘制套牢盘（当前价格以上，红色）
            if loss_prices:
                ax.barh(loss_prices, loss_chips, height=0.01, alpha=0.7, color='lightcoral', edgecolor='red', label='套牢盘')

            # 绘制当前价格线
            ax.axhline(y=current_price, color='black', linestyle='-', linewidth=2, label=f'当前价格: {current_price:.2f}')

            # 绘制平均成本线
            avg_cost = cyq_analysis['avg_cost']
            ax.axhline(y=avg_cost, color='blue', linestyle='--', linewidth=2, label=f'平均成本: {avg_cost:.2f}')

            # 标注90%筹码区间
            chips_90 = cyq_analysis['chips_90']
            price_90_low = float(chips_90['priceRange'][0])
            price_90_high = float(chips_90['priceRange'][1])
            ax.axhspan(price_90_low, price_90_high, alpha=0.2, color='yellow', label='90%筹码区间')

            # 设置图表属性
            actual_days = cyq_analysis['trading_days']  # 获取实际使用的天数
            ax.set_title(f'筹码分布图 ({actual_days}天)', fontsize=11, pad=8)  # 小标题，减少间距
            ax.set_xlabel('筹码量', fontsize=12)
            ax.set_ylabel('价格 (元)', fontsize=12)
            ax.legend()
            ax.grid(True, alpha=0.3)

        except Exception as e:
            print(f"❌ 绘制筹码分布图失败: {e}")
            ax.text(0.5, 0.5, f'筹码分布图绘制失败\n{str(e)}',
                   transform=ax.transAxes, ha='center', va='center')

    def _format_chip_statistics(self, cyq_analysis):
        """格式化筹码统计信息"""
        try:
            current_price = cyq_analysis['current_price']
            avg_cost = cyq_analysis['avg_cost']
            benefit_part = cyq_analysis['benefit_part']
            chips_90 = cyq_analysis['chips_90']
            chips_70 = cyq_analysis['chips_70']

            # 计算各项条件的判断结果
            profit_ratio_good = benefit_part > 0.5  # 获利比例大于50%
            concentration_90_good = chips_90['concentration'] < 0.15  # 90%集中度小于15%
            concentration_70_good = chips_70['concentration'] < 0.10  # 70%集中度小于10%
            avg_cost_good = avg_cost < current_price  # 平均成本低于当前价格

            # 调试信息
            print(f"=== 筹码分布条件判断 ===")
            print(f"获利比例: {benefit_part:.2%} > 50% = {profit_ratio_good}")
            print(f"90%集中度: {chips_90['concentration']*100:.2f}% < 15% = {concentration_90_good}")
            print(f"70%集中度: {chips_70['concentration']*100:.2f}% < 10% = {concentration_70_good}")
            print(f"平均成本: {avg_cost:.2f} < 当前价格: {current_price:.2f} = {avg_cost_good}")

            # 生成标记符号
            def get_mark(condition):
                return "✓" if condition else "✗"

            stats_text = f"""筹码分布统计:
当前价格: {current_price:.2f} 元
平均成本: {avg_cost:.2f} 元 {get_mark(avg_cost_good)}
获利比例: {benefit_part:.2%} {get_mark(profit_ratio_good)}

90%成本区间: {chips_90['priceRange'][0]} - {chips_90['priceRange'][1]}
90%集中度: {chips_90['concentration']*100:.2f}% {get_mark(concentration_90_good)}

70%成本区间: {chips_70['priceRange'][0]} - {chips_70['priceRange'][1]}
70%集中度: {chips_70['concentration']*100:.2f}% {get_mark(concentration_70_good)}

评价标准:
{get_mark(profit_ratio_good)} 获利比例 > 50%
{get_mark(concentration_90_good)} 90%集中度 < 15%
{get_mark(concentration_70_good)} 70%集中度 < 10%
{get_mark(avg_cost_good)} 平均成本 < 当前价格"""

            return stats_text

        except Exception as e:
            return f"统计信息格式化失败: {str(e)}"


# 删除了FengwoChipAnalyzer类 - 只保留CYQ算法





# 删除了create_fengwo_chart方法 - 只保留CYQ算法




# 清理完成，只保留CYQ算法




# 删除重复的StockAnalyzer类定义


# 清理完成，准备真正的StockAnalyzer类



# 最终清理完成




# 删除所有fengwo和传统筹码分布相关内容完成




# 删除完成，只保留CYQ算法




# 最终清理完成




class StockAnalyzer:
    """股票分析主类"""

    def __init__(self, news_analyzer=None):
        # 初始化API，启用多线程和自动重连
        try:
            self.api = TdxHq_API(
                multithread=True,      # 启用多线程支持
                heartbeat=True,        # 启用心跳包
                auto_retry=True,       # 启用自动重试
                raise_exception=False  # 不抛出异常，返回None
            )
        except Exception as e:
            print(f"使用高级参数初始化API失败: {e}，使用基本参数")
            self.api = TdxHq_API()

        # CYQ筹码分析器（延迟初始化，需要股票数据）
        self.cyq_analyzer = None
        self.is_connected = False

        # 引用新闻分析器以访问股票数据
        self.news_analyzer = news_analyzer

        # 临时数据缓存机制（按用户需求）
        # 1. 第一次K线图：从pytdx服务器获取最新数据
        # 2. 第二次筹码分布图：使用临时缓存
        # 3. 绘图完成后：清理临时缓存
        # 4. 再次分析：重新开始循环
        self.temp_stock_data = None      # 临时股票OHLC数据
        self.temp_finance_info = None    # 临时财务信息
        self.temp_stock_code = None      # 临时股票代码
        self.is_first_chart = True       # 是否是第一次绘图（K线图）

# 删除了fengwo筹码分析器 - 只保留CYQ算法

    def start_new_analysis(self):
        """开始新的分析周期，清理临时缓存"""
        self.temp_stock_data = None
        self.temp_finance_info = None
        self.temp_stock_code = None
        self.is_first_chart = True
        print("🔄 开始新的分析周期，临时缓存已清理")

    def clear_temp_cache(self):
        """清理临时缓存（绘图完成后调用）"""
        self.temp_stock_data = None
        self.temp_finance_info = None
        self.temp_stock_code = None
        self.is_first_chart = True
        print("🧹 临时缓存已清理，下次分析将重新获取数据")

    def get_temp_cached_data(self, stock_code):
        """获取临时缓存的股票数据"""
        if (self.temp_stock_data is not None and
            self.temp_stock_code == stock_code and
            not self.is_first_chart):
            print(f"📋 使用临时缓存数据: {stock_code}")
            return self.temp_stock_data, self.temp_finance_info
        return None, None

    def save_temp_data(self, stock_code, stock_data, finance_info):
        """保存临时数据"""
        self.temp_stock_data = stock_data
        self.temp_finance_info = finance_info
        self.temp_stock_code = stock_code
        self.is_first_chart = False  # 标记第一次绘图已完成
        print(f"💾 临时数据已保存: {stock_code}")

        # 初始化 CYQ 筹码分析器
        try:
            self.cyq_analyzer = CYQChipAnalyzer()
            print("✅ CYQ 筹码分析器初始化成功")
        except Exception as e:
            print(f"❌ CYQ 筹码分析器初始化失败: {e}")
            self.cyq_analyzer = None



    # 注意：已移除get_float_shares_akshare方法，因为CYQ算法不需要流通股本数据



    def connect_tdx(self):
        """连接通达信服务器"""
        try:
            # 优先使用测试过的可用服务器
            servers = [
                ('**************', 7709),  # 上海电信主站Z1 (最快)
                ('**************', 80),    # 上海电信主站Z80 (最快)
                ('************', 7709),    # 上证云成都电信一
                ('**************', 7709),  # 上证云北京联通一
                ('*************', 7709),   # 长城国瑞电信1
                ('*************', 7709),   # 长城国瑞电信2
                ('**************', 7709),  # 上海电信主站Z2
                ('***************', 7709), # 北京联通主站Z1
            ]

            print(f"尝试连接 {len(servers)} 个通达信服务器...")

            for i, (host, port) in enumerate(servers):
                try:
                    print(f"正在尝试连接服务器 {i+1}/{len(servers)}: {host}:{port}")

                    # 设置连接超时
                    if self.api.connect(host, port, time_out=5):
                        self.is_connected = True
                        print(f"✅ 成功连接到通达信服务器: {host}:{port}")

                        # 测试连接是否正常工作
                        try:
                            test_data = self.api.get_security_count(0)  # 测试获取深圳市场股票数量
                            if test_data and test_data > 0:
                                print(f"✅ 连接测试成功，深圳市场股票数量: {test_data}")
                                return True
                            else:
                                print(f"❌ 连接测试失败，断开连接: {host}:{port}")
                                self.api.disconnect()
                                self.is_connected = False
                        except Exception as test_e:
                            print(f"❌ 连接测试异常: {test_e}")
                            self.api.disconnect()
                            self.is_connected = False

                except Exception as e:
                    print(f"❌ 连接服务器 {host}:{port} 失败: {e}")
                    continue

            print("❌ 无法连接到任何通达信服务器")
            return False
        except Exception as e:
            print(f"❌ 连接通达信服务器异常: {e}")
            return False

    def get_stock_code_info(self, stock_name):
        """根据股票名称获取股票代码和市场信息"""
        print(f"正在解析股票: {stock_name}")

        # 如果传入的是6位数字代码
        if stock_name.isdigit() and len(stock_name) == 6:
            code = stock_name
            if code.startswith('6'):
                market = 1  # 上海
            else:
                market = 0  # 深圳
            print(f"识别为股票代码: {code}")
            return code, market

        # 如果是股票名称，从已加载的股票数据中查找对应代码
        print(f"识别为股票名称，正在从已加载的股票数据中查找对应代码...")

        # 检查是否有可用的股票数据
        if self.news_analyzer is None:
            print("❌ 未提供新闻分析器引用，无法访问股票数据")
            return None, None

        if not hasattr(self.news_analyzer, 'stock_names') or not hasattr(self.news_analyzer, 'stock_codes'):
            print("❌ 股票数据未加载，请先加载股票数据")
            return None, None

        if not self.news_analyzer.stock_names or not self.news_analyzer.stock_codes:
            print("❌ 股票数据为空，请检查股票数据是否正确加载")
            return None, None

        # 在已加载的股票数据中查找
        try:
            # 查找股票名称在列表中的索引
            if stock_name in self.news_analyzer.stock_names:
                index = self.news_analyzer.stock_names.index(stock_name)
                code = self.news_analyzer.stock_codes[index]
                print(f"✅ 在股票数据中找到对应代码: {stock_name} -> {code}")

                # 判断市场
                if code.startswith('6') or code.startswith('688'):
                    market = 1  # 上海
                elif code.startswith('00') and len(code) == 5:
                    market = 2  # 港股（这里简化处理）
                    print("⚠️ 检测到港股代码，pytdx可能不支持")
                    return None, None
                else:
                    market = 0  # 深圳

                return code, market
            else:
                print(f"❌ 未在股票数据中找到 '{stock_name}' 对应的代码")
                print(f"💡 已加载 {len(self.news_analyzer.stock_names)} 只股票数据")
                # 显示一些相似的股票名称作为提示
                similar_stocks = [name for name in self.news_analyzer.stock_names if stock_name in name or name in stock_name]
                if similar_stocks:
                    print(f"💡 相似的股票名称: {similar_stocks[:5]}")  # 只显示前5个
                return None, None

        except Exception as e:
            print(f"❌ 查找股票代码时出错: {e}")
            return None, None
    
    def get_finance_info(self, stock_code, market):
        """获取股票财务信息（流通股本等）"""
        try:
            print("正在获取财务信息...")

            # 方法1: 尝试使用 get_finance_info 接口
            try:
                # 连接API
                if not self.is_connected:
                    if not self.connect_tdx():
                        print("❌ pytdx API 连接失败")
                        return None

                # 获取财务信息
                finance_info = self.api.get_finance_info(market, stock_code)
                print(finance_info)
                if finance_info:
                    # 流通股本（单位：股）
                    float_shares = finance_info.get('liutongguben', 0)
                    if float_shares and float_shares > 0:
                        print(f"✅ 通过 get_finance_info 获取流通股本: {float_shares} 股")
                        return float_shares
                    else:
                        print("⚠️ get_finance_info 返回的流通股本为空或无效")
                else:
                    print("⚠️ get_finance_info 未返回财务信息")
            except Exception as e:
                print(f"⚠️ get_finance_info 方法失败: {e}")

            print("❌ get_finance_info 获取流通股本失败")
            return None

        except Exception as e:
            print(f"❌ 获取财务信息失败: {e}")
            return None

    def calculate_turnover_rate(self, volume, float_shares):
        """计算换手率"""
        try:
            if float_shares is None or float_shares <= 0:
                return 5.0  # 默认换手率5%

            # volume 单位：手（1手=100股）
            # float_shares 单位：股（从 get_finance_info 直接获取的股数）
            # 换手率 = 成交量(股) / 流通股本(股) * 100%
            volume_shares = volume * 100  # 手转换为股

            # 直接使用流通股本（股）
            float_shares_total = float_shares

            turnover_rate = (volume_shares / float_shares_total) * 100

            # 限制换手率在合理范围内（最大50%，不设最小值限制）
            turnover_rate = min(50.0, turnover_rate)

            # print(f"🔢 换手率计算: {volume_shares:,}股 ÷ {float_shares_total:,}股 × 100% = {turnover_rate:.4f}%")

            return turnover_rate

        except Exception as e:
            print(f"❌ 计算换手率失败: {e}")
            return 5.0  # 默认换手率5%

    def get_stock_data(self, stock_code, market, days=210):
        """获取股票OHLC和换手率数据"""
        try:
            print(f"=== 开始获取股票数据 ===")
            print(f"股票代码: {stock_code}")
            print(f"市场: {'上海' if market == 1 else '深圳'}")

            if days == 0:
                print("获取天数: 所有历史数据（从上市到现在）")
            else:
                print(f"获取天数: {days}")

            # 检查临时缓存（第二次绘图时使用）
            cached_data, cached_finance = self.get_temp_cached_data(stock_code)
            if cached_data is not None:
                print("📋 使用临时缓存数据，跳过网络请求")
                return cached_data

            # 第一次绘图：总是从服务器获取最新数据
            print("🌐 第一次绘图，从服务器获取最新数据")

            # 连接API
            print("正在连接 pytdx API...")
            if not self.is_connected:
                if not self.connect_tdx():
                    print("❌ pytdx API 连接失败")
                    return None

            try:
                # 获取财务信息（流通股本）
                float_shares = self.get_finance_info(stock_code, market)

                # 如果获取流通股本失败，直接返回None，不继续获取K线数据
                if float_shares is None:
                    print("❌ 获取流通股本失败，停止数据获取")
                    return None

                # 获取日K线数据
                print("正在获取日K线数据...")

                if days == 0:
                    # 获取所有历史数据：分批获取，每次最多800条
                    all_data = []
                    start_pos = 0
                    batch_size = 800

                    while True:
                        batch_data = self.api.get_security_bars(9, market, stock_code, start_pos, batch_size)
                        if not batch_data or len(batch_data) == 0:
                            break

                        all_data.extend(batch_data)
                        print(f"已获取 {len(all_data)} 条数据...")

                        # 如果这批数据少于batch_size，说明已经获取完所有数据
                        if len(batch_data) < batch_size:
                            break

                        start_pos += batch_size

                    data = all_data
                    print(f"✅ 成功获取所有历史数据: {len(data)} 条")
                else:
                    # 获取指定天数的数据
                    data = self.api.get_security_bars(9, market, stock_code, 0, days)

                    # 如果获取的数据少于请求的天数，尝试获取所有可用数据
                    if data and len(data) < days:
                        print(f"实际获取到 {len(data)} 天数据，少于请求的 {days} 天")
                        print("尝试获取所有可用的历史数据...")

                        # 分批获取所有历史数据
                        all_data = []
                        start_pos = 0
                        batch_size = 800

                        while True:
                            batch_data = self.api.get_security_bars(9, market, stock_code, start_pos, batch_size)
                            if not batch_data or len(batch_data) == 0:
                                break

                            all_data.extend(batch_data)

                            # 如果这批数据少于batch_size，说明已经获取完所有数据
                            if len(batch_data) < batch_size:
                                break

                            start_pos += batch_size

                        if all_data and len(all_data) > len(data):
                            data = all_data
                            print(f"✅ 成功获取所有历史数据: {len(data)} 条")
                        else:
                            print(f"保持原有数据: {len(data)} 条")

                if not data:
                    print("❌ 未获取到任何数据")
                    return None

                print(f"✅ 最终获取到 {len(data)} 条K线数据")

                # 转换为DataFrame
                df = pd.DataFrame(data)
                print(f"原始数据列: {list(df.columns)}")

                # 重命名列
                df = df.rename(columns={
                    'datetime': 'date',
                    'open': 'open',
                    'high': 'high',
                    'low': 'low',
                    'close': 'close',
                    'vol': 'volume'
                })

                print(f"重命名后列: {list(df.columns)}")

                # 显示前几行原始数据
                print("=== 原始OHLC数据（前5行）===")
                print(df[['date', 'open', 'high', 'low', 'close', 'volume']].head())

                # 计算真实换手率
                if float_shares is not None:
                    print("正在计算真实换手率...")
                    df['turnover_rate'] = df['volume'].apply(
                        lambda vol: self.calculate_turnover_rate(vol, float_shares)
                    )
                    print("✅ 真实换手率计算完成")

                    # 显示换手率统计
                    print(f"换手率范围: {df['turnover_rate'].min():.2f}% - {df['turnover_rate'].max():.2f}%")
                    print(f"平均换手率: {df['turnover_rate'].mean():.2f}%")
                else:
                    print("⚠️ 未获取到流通股本，使用默认换手率")
                    df['turnover_rate'] = 5.0  # 默认换手率5%

                # 确保数据按日期排序
                df = df.sort_values('date').reset_index(drop=True)

                # 显示最终数据统计
                print("=== 最终数据统计 ===")
                print(f"数据行数: {len(df)}")
                print(f"日期范围: {df['date'].min()} 到 {df['date'].max()}")
                print(f"价格范围: {df['low'].min():.2f} - {df['high'].max():.2f}")
                print(f"平均成交量: {df['volume'].mean():.0f}手")

                # 显示最新几天的完整数据（包括换手率）
                print("=== 最新数据（最后3行）===")
                print(df[['date', 'open', 'high', 'low', 'close', 'volume', 'turnover_rate']].tail(3))

                # 保存临时数据（供第二次绘图使用）
                self.save_temp_data(stock_code, df, float_shares)

                return df

            finally:
                # 断开连接
                if self.is_connected:
                    self.api.disconnect()
                    self.is_connected = False
                    print("pytdx API 连接已断开")

        except Exception as e:
            print(f"❌ 获取股票数据失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    # 删除了calculate_chip_distribution方法 - 只保留CYQ算法
    
    # 删除了create_chip_chart方法 - 只保留CYQ算法
    
    # 删除了analyze_stock方法 - 只保留CYQ算法

    # 删除了create_enhanced_chip_chart方法 - 只保留CYQ算法

    # 删除了analyze_stock_enhanced方法 - 只保留CYQ算法

    # 删除了analyze_stock_fengwo方法 - 只保留CYQ算法

    def analyze_stock_cyq(self, stock_name):
        """使用 CYQ 算法进行股票筹码分布分析"""
        print(f"\n{'='*60}")
        print(f"🚀 开始 CYQ 筹码分布分析: {stock_name}")
        print(f"{'='*60}")

        # 开始新的分析周期，清理临时缓存
        self.start_new_analysis()

        # 获取股票代码
        print("📋 步骤1: 解析股票代码...")
        code, market = self.get_stock_code_info(stock_name)
        if code is None:
            error_msg = f"❌ 无法识别股票代码: {stock_name}"
            print(error_msg)
            return None, error_msg

        print(f"✅ 股票代码: {code}, 市场: {'上海' if market == 1 else '深圳'}")

        # 获取股票数据（默认210天，如果不足210天则获取所有可用数据）
        print("\n📊 步骤2: 获取股票数据...")
        stock_data = self.get_stock_data(code, market, days=210)  # 默认210天
        if stock_data is None:
            error_msg = f"❌ 获取股票数据失败: {stock_name}"
            print(error_msg)
            return None, error_msg

        print(f"✅ 成功获取 {len(stock_data)} 天的股票数据")

        # 初始化 CYQ 分析器
        print("\n🧮 步骤3: 初始化 CYQ 算法...")
        try:
            self.cyq_analyzer = CYQChipAnalyzer()
            print("✅ CYQ 分析器初始化成功")
        except Exception as e:
            error_msg = f"❌ CYQ 分析器初始化失败: {e}"
            print(error_msg)
            return None, error_msg

        # 使用 CYQ 算法计算筹码分布
        print("\n🧮 步骤4: 使用 CYQ 算法计算筹码分布...")
        cyq_analysis = self.cyq_analyzer.analyze_stock_with_cyq(stock_data)
        if cyq_analysis is None:
            error_msg = f"❌ CYQ 筹码分布计算失败: {stock_name}"
            print(error_msg)
            return None, error_msg

        print("✅ CYQ 筹码分布计算完成")

        # 创建 CYQ 图表
        print("\n📈 步骤5: 创建 CYQ 筹码分布图表...")
        chart = self.cyq_analyzer.create_cyq_chart(stock_name, cyq_analysis, stock_data)
        if chart is None:
            error_msg = f"❌ 创建 CYQ 图表失败: {stock_name}"
            print(error_msg)
            return None, error_msg

        print("✅ CYQ 图表创建成功")
        print(f"🎉 股票 {stock_name} CYQ 分析完成！")
        print(f"{'='*60}\n")

        # 注意：不在这里清理临时缓存，保留给第二次绘图使用
        # 缓存将在下次分析开始时清理（start_new_analysis）

        return chart, "CYQ 分析完成"


# 测试函数
if __name__ == "__main__":
    analyzer = StockAnalyzer()
    chart, message = analyzer.analyze_stock("600036")  # 招商银行
    print(message)
    if chart:
        plt.show()
