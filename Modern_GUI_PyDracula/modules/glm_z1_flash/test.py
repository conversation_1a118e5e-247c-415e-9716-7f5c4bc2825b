# ///////////////////////////////////////////////////////////////
#
# GLM-Z1-Flash 模块测试
# 测试GLM-Z1-Flash聊天机器人的各项功能
#
# ///////////////////////////////////////////////////////////////

import unittest
import time
from unittest.mock import patch, MagicMock
from .main import GLMChatBot, APIError


class TestGLMChatBot(unittest.TestCase):
    """GLM-Z1-Flash聊天机器人测试类"""

    def setUp(self):
        """测试前准备"""
        self.api_key = "696316314d42490c818e3e5ded184701.xhgEGx1ZbrAsBFy7"
        self.chatbot = GLMChatBot(self.api_key)

    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.chatbot.api_key, self.api_key)
        self.assertEqual(self.chatbot.model, "glm-z1-flash")
        self.assertIsInstance(self.chatbot.sessions, dict)

    def test_create_session(self):
        """测试创建会话"""
        session = self.chatbot.create_session()
        
        self.assertIsInstance(session, dict)
        self.assertIn("id", session)
        self.assertIn("created_at", session)
        self.assertIn("messages", session)
        self.assertIn("title", session)
        
        # 验证会话已存储
        session_id = session["id"]
        self.assertIn(session_id, self.chatbot.sessions)

    def test_rename_session(self):
        """测试重命名会话"""
        session = self.chatbot.create_session()
        session_id = session["id"]
        new_name = "测试会话"
        
        self.chatbot.rename_session(session_id, new_name)
        self.assertEqual(self.chatbot.sessions[session_id]["title"], new_name)

    def test_delete_session(self):
        """测试删除会话"""
        session = self.chatbot.create_session()
        session_id = session["id"]
        
        self.chatbot.delete_session(session_id)
        self.assertNotIn(session_id, self.chatbot.sessions)

    def test_get_history(self):
        """测试获取会话历史"""
        session = self.chatbot.create_session()
        session_id = session["id"]
        
        history = self.chatbot.get_history(session_id)
        self.assertIsInstance(history, list)
        self.assertEqual(len(history), 0)  # 新会话应该没有历史

    def test_get_session_list(self):
        """测试获取会话列表"""
        # 创建几个会话
        session1 = self.chatbot.create_session()
        session2 = self.chatbot.create_session()
        
        sessions = self.chatbot.get_session_list()
        self.assertIsInstance(sessions, list)
        self.assertGreaterEqual(len(sessions), 2)

    def test_get_headers(self):
        """测试获取请求头"""
        headers = self.chatbot._GLMChatBot__get_headers()
        
        self.assertIn("Authorization", headers)
        self.assertIn("Content-Type", headers)
        self.assertEqual(headers["Authorization"], f"Bearer {self.api_key}")
        self.assertEqual(headers["Content-Type"], "application/json")

    def test_session_not_found_error(self):
        """测试会话不存在的错误处理"""
        fake_session_id = "non-existent-session"
        
        with self.assertRaises(APIError):
            self.chatbot.get_history(fake_session_id)
        
        with self.assertRaises(APIError):
            self.chatbot.rename_session(fake_session_id, "new name")
        
        with self.assertRaises(APIError):
            self.chatbot.delete_session(fake_session_id)

    @patch('requests.post')
    def test_chat_non_stream_success(self, mock_post):
        """测试非流式对话成功情况"""
        # 模拟API响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.iter_lines.return_value = [
            b'data: {"choices": [{"delta": {"content": "Hello"}}]}',
            b'data: {"choices": [{"delta": {"content": " World"}}]}',
            b'data: [DONE]'
        ]
        mock_post.return_value = mock_response
        
        # 创建会话并测试对话
        session = self.chatbot.create_session()
        session_id = session["id"]
        
        response = self.chatbot.chat(
            session_id=session_id,
            prompt="Hello",
            stream=False
        )
        
        self.assertIsInstance(response, dict)
        self.assertIn("content", response)
        self.assertIn("message_id", response)

    @patch('requests.post')
    def test_chat_api_error(self, mock_post):
        """测试API错误情况"""
        # 模拟API错误响应
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        mock_post.return_value = mock_response
        
        session = self.chatbot.create_session()
        session_id = session["id"]
        
        with self.assertRaises(APIError):
            self.chatbot.chat(
                session_id=session_id,
                prompt="Hello",
                stream=False
            )

    def test_multiple_sessions(self):
        """测试多会话管理"""
        sessions = []
        
        # 创建多个会话
        for i in range(3):
            session = self.chatbot.create_session()
            self.chatbot.rename_session(session["id"], f"会话{i+1}")
            sessions.append(session)
        
        # 验证会话数量
        session_list = self.chatbot.get_session_list()
        self.assertGreaterEqual(len(session_list), 3)
        
        # 验证会话名称
        for i, session in enumerate(sessions):
            stored_session = self.chatbot.sessions[session["id"]]
            self.assertEqual(stored_session["title"], f"会话{i+1}")


class TestIntegration(unittest.TestCase):
    """集成测试类"""

    def setUp(self):
        """测试前准备"""
        self.api_key = "696316314d42490c818e3e5ded184701.xhgEGx1ZbrAsBFy7"
        self.chatbot = GLMChatBot(self.api_key)

    def test_sentiment_analysis_workflow(self):
        """测试情绪分析工作流程"""
        # 创建会话
        session = self.chatbot.create_session()
        session_id = session["id"]
        
        # 模拟情绪分析提示词
        news_content = "某公司发布了新产品，预计将提升业绩"
        stock_name = "某公司"
        
        prompt = f"""请分析以下新闻内容对股票"{stock_name}"的市场情绪。

新闻内容：{news_content}

重要要求：
1. 只允许回答以下三种情况之一："正面"、"负面"、"未知"
2. 请只回答这三个词中的一个，不要添加任何解释、说明或其他内容"""
        
        # 这里只测试接口调用，不测试实际API响应
        # 在实际使用中需要网络连接
        try:
            # 注意：这个测试需要真实的API连接
            # response = self.chatbot.chat(
            #     session_id=session_id,
            #     prompt=prompt,
            #     stream=False
            # )
            # self.assertIn("content", response)
            pass
        except Exception as e:
            # 如果没有网络连接，跳过这个测试
            self.skipTest(f"需要网络连接进行集成测试: {e}")

    def test_compatibility_with_revdeepseek(self):
        """测试与revDeepSeek接口的兼容性"""
        # 验证接口方法存在
        self.assertTrue(hasattr(self.chatbot, 'create_session'))
        self.assertTrue(hasattr(self.chatbot, 'chat'))
        self.assertTrue(hasattr(self.chatbot, 'rename_session'))
        self.assertTrue(hasattr(self.chatbot, 'delete_session'))
        self.assertTrue(hasattr(self.chatbot, 'get_history'))
        self.assertTrue(hasattr(self.chatbot, 'get_session_list'))
        
        # 验证chat方法参数兼容性
        session = self.chatbot.create_session()
        session_id = session["id"]
        
        # 这些调用应该不会抛出参数错误
        try:
            # 模拟调用，不实际发送请求
            pass
        except TypeError:
            self.fail("接口不兼容revDeepSeek")


def run_tests():
    """运行所有测试"""
    print("=== GLM-Z1-Flash 模块测试 ===")
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTests(loader.loadTestsFromTestCase(TestGLMChatBot))
    suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！")
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    run_tests()
