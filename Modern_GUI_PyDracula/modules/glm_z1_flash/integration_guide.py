# ///////////////////////////////////////////////////////////////
#
# GLM-Z1-Flash 集成指南
# 展示如何在现有项目中集成GLM-Z1-Flash模块
#
# ///////////////////////////////////////////////////////////////

"""
GLM-Z1-Flash 模块集成指南

本文件展示如何在现有的新闻分析系统中集成GLM-Z1-Flash模块，
替换或补充现有的revDeepSeek模块。
"""


def integrate_with_news_analyzer():
    """
    在新闻分析器中集成GLM-Z1-Flash
    
    这个示例展示如何修改现有的NewsAnalyzer类来使用GLM-Z1-Flash
    """
    
    # 1. 导入新模块
    from ..glm_z1_flash import GLMChatBot, APIError
    
    # 2. 在NewsAnalyzer类的__init__方法中添加GLM初始化
    """
    class NewsAnalyzer:
        def __init__(self):
            # 现有代码...
            self.chatbot = None
            self.glm_chatbot = None  # 新增GLM聊天机器人
            self.session_id = None
            self.glm_session_id = None  # 新增GLM会话ID
            # ...
    """
    
    # 3. 修改init_chatbot方法支持GLM-Z1-Flash
    """
    def init_chatbot(self, token: str = None, use_glm: bool = False) -> bool:
        '''初始化AI聊天机器人'''
        
        if use_glm:
            # 使用GLM-Z1-Flash
            try:
                if token is None:
                    token = self.db.get_token()
                    if token is None:
                        print("❌ 未找到Token，请在设置中配置AI聊天机器人Token")
                        return False
                
                print("=== 初始化GLM-Z1-Flash聊天机器人 ===")
                self.glm_chatbot = GLMChatBot(token)
                session_res = self.glm_chatbot.create_session()
                self.glm_session_id = session_res["id"]
                print("✅ GLM-Z1-Flash分析模块初始化成功")
                return True
                
            except Exception as e:
                print(f"❌ GLM-Z1-Flash分析模块初始化失败: {e}")
                return False
        else:
            # 使用原有的revDeepSeek（保持兼容性）
            # 原有代码...
    """
    
    # 4. 修改analyze_sentiment方法支持GLM
    """
    def analyze_sentiment(self, news_content: str, stock_info: str, use_glm: bool = False) -> str:
        '''使用chatbot分析新闻对股票的市场情绪'''
        
        if use_glm and self.glm_chatbot and self.glm_session_id:
            return self._analyze_sentiment_with_glm(news_content, stock_info)
        elif self.chatbot and self.session_id:
            return self._analyze_sentiment_with_deepseek(news_content, stock_info)
        else:
            return "未知"
    
    def _analyze_sentiment_with_glm(self, news_content: str, stock_info: str) -> str:
        '''使用GLM-Z1-Flash分析情绪'''
        
        prompt = f'''请分析以下新闻内容对股票"{stock_info}"的市场情绪。

新闻内容：{news_content}

重要要求：
1. 只允许回答以下三种情况之一："正面"、"负面"、"未知"
2. 如果新闻对股票有明显的积极影响，回答"正面"
3. 如果新闻对股票有明显的消极影响，回答"负面"
4. 如果无法判断或影响不明确，回答"未知"
5. 请只回答这三个词中的一个，不要添加任何解释、说明或其他内容'''

        max_attempts = 2
        
        for attempt in range(max_attempts):
            try:
                response = self.glm_chatbot.chat(
                    session_id=self.glm_session_id,
                    prompt=prompt,
                    stream=False
                )
                
                sentiment = response.get('content', '').strip()
                print(f"GLM情绪分析响应 (尝试 {attempt + 1}): {sentiment}")
                
                # 检查回答是否为标准的三种情况
                if sentiment in ["正面", "负面", "未知"]:
                    print(f"GLM情绪分析成功: {sentiment}")
                    return sentiment
                elif "正面" in sentiment and "负面" not in sentiment and "未知" not in sentiment:
                    return "正面"
                elif "负面" in sentiment and "正面" not in sentiment and "未知" not in sentiment:
                    return "负面"
                elif "未知" in sentiment and "正面" not in sentiment and "负面" not in sentiment:
                    return "未知"
                else:
                    if attempt < max_attempts - 1:
                        print(f"GLM响应格式不正确，重试第 {attempt + 2} 次...")
                        continue
                    else:
                        print("GLM连续2次无法获得正确格式的响应，默认返回'未知'")
                        return "未知"
                        
            except Exception as e:
                print(f"GLM情绪分析出错 (尝试 {attempt + 1}): {e}")
                if attempt < max_attempts - 1:
                    continue
                else:
                    return "未知"
        
        return "未知"
    """


def database_integration():
    """
    数据库集成示例
    
    展示如何在数据库中存储GLM相关的配置
    """
    
    # 在NewsDatabase类中添加GLM配置管理
    """
    class NewsDatabase:
        def save_glm_token(self, token: str) -> bool:
            '''保存GLM API Token'''
            try:
                cursor = self.conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO tokens (name, value) 
                    VALUES (?, ?)
                ''', ('glm_api_key', token))
                self.conn.commit()
                return True
            except Exception as e:
                print(f"保存GLM Token失败: {e}")
                return False
        
        def get_glm_token(self) -> str:
            '''获取GLM API Token'''
            try:
                cursor = self.conn.cursor()
                cursor.execute('SELECT value FROM tokens WHERE name = ?', ('glm_api_key',))
                result = cursor.fetchone()
                return result[0] if result else None
            except Exception as e:
                print(f"获取GLM Token失败: {e}")
                return None
        
        def save_ai_preference(self, use_glm: bool) -> bool:
            '''保存AI模型偏好设置'''
            try:
                cursor = self.conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO settings (key, value) 
                    VALUES (?, ?)
                ''', ('use_glm', '1' if use_glm else '0'))
                self.conn.commit()
                return True
            except Exception as e:
                print(f"保存AI偏好设置失败: {e}")
                return False
        
        def get_ai_preference(self) -> bool:
            '''获取AI模型偏好设置'''
            try:
                cursor = self.conn.cursor()
                cursor.execute('SELECT value FROM settings WHERE key = ?', ('use_glm',))
                result = cursor.fetchone()
                return result[0] == '1' if result else False
            except Exception as e:
                print(f"获取AI偏好设置失败: {e}")
                return False
    """


def gui_integration():
    """
    GUI集成示例
    
    展示如何在GUI中添加GLM相关的设置选项
    """
    
    # 在设置页面添加AI模型选择
    """
    # 在UI中添加单选按钮或下拉框
    self.ai_model_group = QButtonGroup()
    self.deepseek_radio = QRadioButton("DeepSeek")
    self.glm_radio = QRadioButton("GLM-Z1-Flash")
    
    self.ai_model_group.addButton(self.deepseek_radio, 0)
    self.ai_model_group.addButton(self.glm_radio, 1)
    
    # 添加GLM API Key输入框
    self.glm_token_input = QLineEdit()
    self.glm_token_input.setPlaceholderText("请输入GLM-Z1-Flash API Key")
    self.glm_token_input.setEchoMode(QLineEdit.Password)
    
    # 保存设置的方法
    def save_ai_settings(self):
        use_glm = self.glm_radio.isChecked()
        glm_token = self.glm_token_input.text()
        
        # 保存到数据库
        self.db.save_ai_preference(use_glm)
        if glm_token:
            self.db.save_glm_token(glm_token)
        
        # 重新初始化AI模块
        if use_glm:
            self.news_analyzer.init_chatbot(glm_token, use_glm=True)
        else:
            self.news_analyzer.init_chatbot(use_glm=False)
    """


def error_handling_best_practices():
    """
    错误处理最佳实践
    
    展示如何优雅地处理GLM API的各种错误情况
    """
    
    """
    def robust_sentiment_analysis(self, news_content: str, stock_info: str) -> str:
        '''健壮的情绪分析，支持多模型回退'''
        
        # 首先尝试GLM-Z1-Flash
        if self.glm_chatbot and self.glm_session_id:
            try:
                result = self._analyze_sentiment_with_glm(news_content, stock_info)
                if result in ["正面", "负面", "未知"]:
                    return result
                else:
                    print("GLM返回格式不正确，尝试DeepSeek")
            except APIError as e:
                print(f"GLM API错误，尝试DeepSeek: {e}")
            except Exception as e:
                print(f"GLM未知错误，尝试DeepSeek: {e}")
        
        # 回退到DeepSeek
        if self.chatbot and self.session_id:
            try:
                return self._analyze_sentiment_with_deepseek(news_content, stock_info)
            except Exception as e:
                print(f"DeepSeek也失败了: {e}")
        
        # 最后回退到默认值
        print("所有AI模型都失败，返回默认值")
        return "未知"
    """


def performance_optimization():
    """
    性能优化建议
    
    展示如何优化GLM-Z1-Flash的使用性能
    """
    
    """
    # 1. 连接池管理
    class GLMConnectionPool:
        def __init__(self, api_key: str, pool_size: int = 3):
            self.api_key = api_key
            self.pool = []
            for _ in range(pool_size):
                chatbot = GLMChatBot(api_key)
                session = chatbot.create_session()
                self.pool.append((chatbot, session["id"]))
        
        def get_chatbot(self):
            if self.pool:
                return self.pool.pop()
            else:
                # 如果池为空，创建新的
                chatbot = GLMChatBot(self.api_key)
                session = chatbot.create_session()
                return (chatbot, session["id"])
        
        def return_chatbot(self, chatbot, session_id):
            self.pool.append((chatbot, session_id))
    
    # 2. 异步处理
    import asyncio
    
    async def async_sentiment_analysis(self, news_items: list) -> list:
        '''异步批量情绪分析'''
        
        async def analyze_single(news_item):
            try:
                # 这里需要实现异步版本的GLM调用
                return await self._async_analyze_sentiment_with_glm(
                    news_item['content'], 
                    news_item['stock']
                )
            except Exception as e:
                print(f"异步分析失败: {e}")
                return "未知"
        
        # 并发处理多个新闻项
        tasks = [analyze_single(item) for item in news_items]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return results
    
    # 3. 缓存机制
    from functools import lru_cache
    import hashlib
    
    @lru_cache(maxsize=1000)
    def cached_sentiment_analysis(self, content_hash: str, stock_info: str) -> str:
        '''带缓存的情绪分析'''
        # 实际的分析逻辑
        return self._analyze_sentiment_with_glm(content_hash, stock_info)
    
    def analyze_with_cache(self, news_content: str, stock_info: str) -> str:
        # 生成内容哈希作为缓存键
        content_hash = hashlib.md5(news_content.encode()).hexdigest()
        return self.cached_sentiment_analysis(content_hash, stock_info)
    """


if __name__ == "__main__":
    print("GLM-Z1-Flash 集成指南")
    print("=" * 50)
    print("1. 基本集成 - integrate_with_news_analyzer()")
    print("2. 数据库集成 - database_integration()")
    print("3. GUI集成 - gui_integration()")
    print("4. 错误处理 - error_handling_best_practices()")
    print("5. 性能优化 - performance_optimization()")
    print("=" * 50)
    print("请查看各个函数的文档字符串了解详细的集成方法。")
