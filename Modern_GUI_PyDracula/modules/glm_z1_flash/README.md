# GLM-Z1-Flash AI聊天机器人模块

基于智谱AI GLM-Z1-Flash模型的Python聊天机器人模块，提供简单易用的API接口。

## 功能特性

- ✅ 支持GLM-Z1-Flash模型对话
- ✅ 会话管理（创建、删除、重命名）
- ✅ 流式和非流式响应
- ✅ 消息历史记录
- ✅ 错误处理和重试机制
- ✅ 与现有revDeepSeek模块接口兼容

## 安装依赖

```bash
pip install requests
```

## 快速开始

### 1. 基本使用

```python
from modules.glm_z1_flash import GLMChatBot

# 初始化聊天机器人
api_key = "你的API密钥"
chatbot = GLMChatBot(api_key)

# 创建会话
session = chatbot.create_session()
session_id = session["id"]

# 进行对话
response = chatbot.chat(
    session_id=session_id,
    prompt="你好，请介绍一下你自己",
    stream=False
)

print(response["content"])
```

### 2. 流式响应

```python
# 流式对话
for chunk in chatbot.chat(
    session_id=session_id,
    prompt="请详细解释股票情绪分析",
    stream=True
):
    content = chunk.get("content", "")
    print(content, end="", flush=True)
```

### 3. 情绪分析专用

```python
# 情绪分析提示词
prompt = f"""请分析以下新闻内容对股票"{stock_name}"的市场情绪。

新闻内容：{news_content}

重要要求：
1. 只允许回答以下三种情况之一："正面"、"负面"、"未知"
2. 如果新闻对股票有明显的积极影响，回答"正面"
3. 如果新闻对股票有明显的消极影响，回答"负面"
4. 如果无法判断或影响不明确，回答"未知"
5. 请只回答这三个词中的一个，不要添加任何解释、说明或其他内容"""

response = chatbot.chat(
    session_id=session_id,
    prompt=prompt,
    stream=False
)

sentiment = response["content"].strip()
```

## API参考

### GLMChatBot类

#### 初始化
```python
chatbot = GLMChatBot(api_key: str)
```

#### 方法

##### create_session() -> Dict
创建新的对话会话
- 返回: 包含会话ID和创建时间的字典

##### chat(session_id, prompt, **kwargs) -> Union[Dict, Generator]
进行对话
- `session_id`: 会话ID
- `prompt`: 用户输入内容
- `parent_message_id`: 父消息ID（兼容性参数）
- `search`: 是否启用搜索（GLM-Z1-Flash不支持）
- `thinking`: 是否启用思考模式
- `file`: 文件内容（GLM-Z1-Flash暂不支持）
- `stream`: 是否使用流式响应
- 返回: 响应字典或流式生成器

##### rename_session(session_id: str, name: str) -> None
重命名会话

##### delete_session(session_id: str) -> None
删除会话

##### get_history(session_id: str) -> List
获取会话历史消息

##### get_session_list(count: int = 100) -> List
获取会话列表

## 与现有代码集成

该模块设计为与现有的`revDeepSeek`模块接口兼容，可以直接替换使用：

```python
# 原来的代码
from modules.revDeepSeek import ChatBot
chatbot = ChatBot(token)

# 替换为GLM-Z1-Flash
from modules.glm_z1_flash import GLMChatBot
chatbot = GLMChatBot(api_key)

# 其他调用方式保持不变
session = chatbot.create_session()
response = chatbot.chat(session_id, prompt, stream=False)
```

## 错误处理

模块提供了`APIError`异常类来处理各种错误情况：

```python
from modules.glm_z1_flash import GLMChatBot, APIError

try:
    response = chatbot.chat(session_id, prompt)
except APIError as e:
    print(f"API错误: {e}")
except Exception as e:
    print(f"其他错误: {e}")
```

## 配置说明

### API密钥
- 获取地址: https://bigmodel.cn/
- 模型: GLM-Z1-Flash（免费模型）
- 当前配置的API密钥: `696316314d42490c818e3e5ded184701.xhgEGx1ZbrAsBFy7`

### 模型参数
- 模型名称: `glm-z1-flash`
- 最大tokens: 4096
- 温度: 0.7
- 支持流式响应: ✅
- 支持思考模式: ✅

## 注意事项

1. **网络连接**: 需要稳定的网络连接访问智谱AI API
2. **API限制**: 请遵守智谱AI的API使用限制和条款
3. **错误重试**: 建议在生产环境中实现适当的重试机制
4. **会话管理**: 会话信息存储在内存中，程序重启后会丢失

## 示例代码

查看 `example.py` 文件获取更多使用示例。

## 更新日志

### v1.0.0 (2025-07-01)
- 初始版本发布
- 支持基本对话功能
- 支持流式响应
- 支持会话管理
- 与revDeepSeek接口兼容
