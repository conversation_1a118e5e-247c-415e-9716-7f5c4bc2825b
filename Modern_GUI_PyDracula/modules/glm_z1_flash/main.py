# ///////////////////////////////////////////////////////////////
#
# GLM-Z1-Flash AI聊天机器人
# 基于智谱AI GLM-Z1-Flash模型
# API文档: https://bigmodel.cn/dev/api/Reasoning-models/glm-z1
#
# ///////////////////////////////////////////////////////////////

import requests
import json
import uuid
from typing import Dict, Generator, Union, Optional
import time


class APIError(Exception):
    """API错误"""
    def __init__(self, message: str):
        self.message = message

    def __str__(self):
        return self.message


class GLMChatBot:
    """GLM-Z1-Flash聊天机器人类"""

    def __init__(self, api_key: str):
        """
        初始化GLM-Z1-Flash聊天机器人
        :param api_key: 智谱AI API密钥
        """
        self.api_key = api_key
        self.api_base = "https://open.bigmodel.cn/api/paas/v4"
        self.model = "glm-z1-flash"
        self.sessions = {}  # 存储会话信息

    def __get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'GLM-Z1-Flash-Client/1.0.0'
        }

    def create_session(self) -> Dict:
        """
        创建会话
        :return: 会话信息
        """
        try:
            # 生成唯一的会话ID
            session_id = str(uuid.uuid4())
            
            # 创建会话记录
            session_info = {
                "id": session_id,
                "created_at": int(time.time()),
                "messages": [],
                "title": f"会话_{session_id[:8]}"
            }
            
            # 存储会话信息
            self.sessions[session_id] = session_info
            
            print(f"✅ GLM-Z1-Flash 会话创建成功: {session_id}")
            return session_info
            
        except Exception as e:
            raise APIError(f"创建会话失败: {str(e)}")

    def rename_session(self, session_id: str, name: str) -> None:
        """
        重命名会话
        :param session_id: 会话ID
        :param name: 新名称
        """
        if session_id in self.sessions:
            self.sessions[session_id]["title"] = name
            print(f"✅ 会话重命名成功: {name}")
        else:
            raise APIError(f"会话不存在: {session_id}")

    def delete_session(self, session_id: str) -> None:
        """
        删除会话
        :param session_id: 会话ID
        """
        if session_id in self.sessions:
            del self.sessions[session_id]
            print(f"✅ 会话删除成功: {session_id}")
        else:
            raise APIError(f"会话不存在: {session_id}")

    def get_history(self, session_id: str) -> list:
        """
        获取会话历史
        :param session_id: 会话ID
        :return: 消息历史列表
        """
        if session_id in self.sessions:
            return self.sessions[session_id]["messages"]
        else:
            raise APIError(f"会话不存在: {session_id}")

    def get_session_list(self, count: int = 100) -> list:
        """
        获取会话列表
        :param count: 返回数量限制
        :return: 会话列表
        """
        sessions = list(self.sessions.values())
        return sessions[:count]

    def __stream_chat(
        self,
        session_id: str,
        prompt: str,
        parent_message_id: str = None,
        search: bool = False,
        thinking: bool = False,
        file: Union[bytes, str] = None
    ) -> Generator[Dict, None, None]:
        """
        流式对话
        :param session_id: 会话ID
        :param prompt: 用户输入
        :param parent_message_id: 父消息ID（兼容性参数）
        :param search: 是否启用搜索（GLM-Z1-Flash不支持）
        :param thinking: 是否启用思考模式
        :param file: 文件内容（GLM-Z1-Flash暂不支持文件上传）
        :return: 流式响应生成器
        """
        if session_id not in self.sessions:
            raise APIError(f"会话不存在: {session_id}")

        # 构建消息历史
        messages = []
        
        # 添加历史消息
        for msg in self.sessions[session_id]["messages"]:
            messages.append({
                "role": msg["role"],
                "content": msg["content"]
            })
        
        # 添加当前用户消息
        messages.append({
            "role": "user",
            "content": prompt
        })

        # 构建请求数据
        request_data = {
            "model": self.model,
            "messages": messages,
            "stream": True,
            "temperature": 0.7,
            "max_tokens": 4096
        }

        # 如果启用思考模式，添加相关参数
        if thinking:
            request_data["thinking"] = True

        url = f"{self.api_base}/chat/completions"
        
        try:
            response = requests.post(
                url,
                headers=self.__get_headers(),
                json=request_data,
                stream=True,
                timeout=60
            )
            
            if response.status_code != 200:
                raise APIError(f"API请求失败: {response.status_code} - {response.text}")

            # 处理流式响应
            full_content = ""
            thinking_content = ""
            message_id = str(uuid.uuid4())
            
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data_str = line[6:]  # 移除 'data: ' 前缀
                        
                        if data_str.strip() == '[DONE]':
                            break
                            
                        try:
                            data = json.loads(data_str)
                            choices = data.get('choices', [])
                            
                            if choices:
                                delta = choices[0].get('delta', {})
                                content = delta.get('content', '')
                                
                                if content:
                                    full_content += content
                                    
                                    yield {
                                        "content": content,
                                        "type": "text",
                                        "message_id": message_id
                                    }
                                    
                        except json.JSONDecodeError:
                            continue
            
            # 保存消息到会话历史
            self.sessions[session_id]["messages"].extend([
                {"role": "user", "content": prompt, "message_id": str(uuid.uuid4())},
                {"role": "assistant", "content": full_content, "message_id": message_id}
            ])
            
        except requests.exceptions.RequestException as e:
            raise APIError(f"网络请求失败: {str(e)}")
        except Exception as e:
            raise APIError(f"流式对话失败: {str(e)}")

    def chat(
        self,
        session_id: str,
        prompt: str,
        parent_message_id: str = None,
        search: bool = False,
        thinking: bool = False,
        file: Union[bytes, str] = None,
        stream: bool = False
    ) -> Union[Dict, Generator[Dict, None, None]]:
        """
        对话接口
        :param session_id: 会话ID
        :param prompt: 用户输入
        :param parent_message_id: 父消息ID（兼容性参数）
        :param search: 是否启用搜索（GLM-Z1-Flash不支持）
        :param thinking: 是否启用思考模式
        :param file: 文件内容（GLM-Z1-Flash暂不支持文件上传）
        :param stream: 是否使用流式响应
        :return: 响应结果或流式生成器
        """
        if stream:
            return self.__stream_chat(
                session_id, prompt, parent_message_id, search, thinking, file
            )
        else:
            # 非流式响应：收集所有流式数据
            full_content = ""
            thinking_content = ""
            message_id = None
            
            for chunk in self.__stream_chat(
                session_id, prompt, parent_message_id, search, thinking, file
            ):
                content = chunk.get("content", "")
                chunk_type = chunk.get("type", "")
                
                if chunk_type == "text":
                    full_content += content
                elif chunk_type == "thinking":
                    thinking_content += content
                    
                message_id = chunk.get("message_id")
            
            result = {
                "content": full_content,
                "message_id": message_id
            }
            
            if thinking and thinking_content:
                result["thinking_content"] = thinking_content
                
            return result
