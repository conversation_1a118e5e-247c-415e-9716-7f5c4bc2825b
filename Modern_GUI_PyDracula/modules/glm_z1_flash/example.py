# ///////////////////////////////////////////////////////////////
#
# GLM-Z1-Flash 使用示例
# 演示如何使用GLM-Z1-Flash聊天机器人
#
# ///////////////////////////////////////////////////////////////

import asyncio
from .main import GLMChatBot, APIError


def example_basic_usage():
    """基本使用示例"""
    print("=== GLM-Z1-Flash 基本使用示例 ===")
    
    # 初始化聊天机器人
    api_key = "696316314d42490c818e3e5ded184701.xhgEGx1ZbrAsBFy7"
    chatbot = GLMChatBot(api_key)
    
    try:
        # 创建会话
        session = chatbot.create_session()
        session_id = session["id"]
        print(f"会话创建成功: {session_id}")
        
        # 进行对话
        prompt = "你好，请简单介绍一下你自己。"
        print(f"用户: {prompt}")
        
        response = chatbot.chat(
            session_id=session_id,
            prompt=prompt,
            stream=False
        )
        
        print(f"助手: {response['content']}")
        
        # 继续对话
        prompt2 = "请分析以下新闻对股票市场的影响：某公司发布了新产品，预计将提升业绩。"
        print(f"用户: {prompt2}")
        
        response2 = chatbot.chat(
            session_id=session_id,
            prompt=prompt2,
            stream=False
        )
        
        print(f"助手: {response2['content']}")
        
        # 查看会话历史
        history = chatbot.get_history(session_id)
        print(f"会话历史消息数量: {len(history)}")
        
    except APIError as e:
        print(f"API错误: {e}")
    except Exception as e:
        print(f"其他错误: {e}")


def example_stream_usage():
    """流式响应使用示例"""
    print("\n=== GLM-Z1-Flash 流式响应示例 ===")
    
    api_key = "696316314d42490c818e3e5ded184701.xhgEGx1ZbrAsBFy7"
    chatbot = GLMChatBot(api_key)
    
    try:
        # 创建会话
        session = chatbot.create_session()
        session_id = session["id"]
        
        prompt = "请详细解释什么是股票情绪分析？"
        print(f"用户: {prompt}")
        print("助手: ", end="", flush=True)
        
        # 流式对话
        for chunk in chatbot.chat(
            session_id=session_id,
            prompt=prompt,
            stream=True
        ):
            content = chunk.get("content", "")
            print(content, end="", flush=True)
        
        print()  # 换行
        
    except APIError as e:
        print(f"API错误: {e}")
    except Exception as e:
        print(f"其他错误: {e}")


def example_sentiment_analysis():
    """情绪分析专用示例"""
    print("\n=== GLM-Z1-Flash 情绪分析示例 ===")
    
    api_key = "696316314d42490c818e3e5ded184701.xhgEGx1ZbrAsBFy7"
    chatbot = GLMChatBot(api_key)
    
    try:
        # 创建会话
        session = chatbot.create_session()
        session_id = session["id"]
        
        # 情绪分析提示词
        news_content = "**【财联社6月30日讯】某科技公司发布了革命性的新产品，预计将大幅提升公司业绩，股价有望迎来上涨。**"
        stock_name = "某科技公司"
        
        prompt = f"""请分析以下新闻内容对股票"{stock_name}"的市场情绪。

新闻内容：{news_content}

重要要求：
1. 只允许回答以下三种情况之一："正面"、"负面"、"未知"
2. 如果新闻对股票有明显的积极影响，回答"正面"
3. 如果新闻对股票有明显的消极影响，回答"负面"
4. 如果无法判断或影响不明确，回答"未知"
5. 请只回答这三个词中的一个，不要添加任何解释、说明或其他内容"""
        
        print(f"新闻内容: {news_content}")
        print(f"股票: {stock_name}")
        print("分析中...")
        
        response = chatbot.chat(
            session_id=session_id,
            prompt=prompt,
            stream=False
        )
        
        sentiment = response['content'].strip()
        print(f"情绪分析结果: {sentiment}")
        
        # 验证结果格式
        if sentiment in ["正面", "负面", "未知"]:
            print("✅ 分析结果格式正确")
        else:
            print("❌ 分析结果格式不正确，需要重试")
        
    except APIError as e:
        print(f"API错误: {e}")
    except Exception as e:
        print(f"其他错误: {e}")


def example_session_management():
    """会话管理示例"""
    print("\n=== GLM-Z1-Flash 会话管理示例 ===")
    
    api_key = "696316314d42490c818e3e5ded184701.xhgEGx1ZbrAsBFy7"
    chatbot = GLMChatBot(api_key)
    
    try:
        # 创建多个会话
        session1 = chatbot.create_session()
        session2 = chatbot.create_session()
        
        # 重命名会话
        chatbot.rename_session(session1["id"], "情绪分析会话")
        chatbot.rename_session(session2["id"], "股票分析会话")
        
        # 获取会话列表
        sessions = chatbot.get_session_list()
        print(f"当前会话数量: {len(sessions)}")
        
        for session in sessions:
            print(f"会话ID: {session['id'][:8]}..., 标题: {session['title']}")
        
        # 删除会话
        chatbot.delete_session(session2["id"])
        print(f"删除会话后，剩余会话数量: {len(chatbot.get_session_list())}")
        
    except APIError as e:
        print(f"API错误: {e}")
    except Exception as e:
        print(f"其他错误: {e}")


if __name__ == "__main__":
    # 运行所有示例
    example_basic_usage()
    example_stream_usage()
    example_sentiment_analysis()
    example_session_management()
    
    print("\n=== 所有示例运行完成 ===")
