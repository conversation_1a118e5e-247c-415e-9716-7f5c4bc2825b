"""
GLM-Z1-Flash 客户端使用示例
展示如何在其他模块中使用 GLM-Z1-Flash 客户端
"""

from .glm_z1_client import GLMZ1Client, GLMError, create_glm_client


class NewsAnalyzer:
    """新闻分析器示例类，展示如何集成GLM-Z1-Flash"""
    
    def __init__(self, api_key: str):
        """
        初始化新闻分析器
        
        Args:
            api_key (str): GLM API密钥
        """
        self.glm_client = create_glm_client(api_key)
        
    def analyze_sentiment(self, news_content: str) -> str:
        """
        分析新闻情感
        
        Args:
            news_content (str): 新闻内容
            
        Returns:
            str: 情感分析结果 ('正面', '负面', '未知')
        """
        try:
            return self.glm_client.sentiment_analysis(news_content)
        except GLMError as e:
            print(f"情感分析失败: {e}")
            return '未知'
    
    def summarize_news(self, news_content: str, max_length: int = 100) -> str:
        """
        生成新闻摘要
        
        Args:
            news_content (str): 新闻内容
            max_length (int): 摘要最大长度
            
        Returns:
            str: 新闻摘要
        """
        prompt = f"请为以下新闻生成一个简洁的摘要（不超过{max_length}字）：\n\n{news_content}"
        
        try:
            return self.glm_client.chat(
                prompt=prompt,
                system_prompt="你是一个专业的新闻摘要生成助手，擅长提取关键信息。",
                temperature=0.3,
                max_tokens=max_length * 2  # 预留一些token空间
            )
        except GLMError as e:
            print(f"摘要生成失败: {e}")
            return "摘要生成失败"
    
    def extract_keywords(self, news_content: str, num_keywords: int = 5) -> list:
        """
        提取新闻关键词
        
        Args:
            news_content (str): 新闻内容
            num_keywords (int): 关键词数量
            
        Returns:
            list: 关键词列表
        """
        prompt = f"请从以下新闻中提取{num_keywords}个最重要的关键词，用逗号分隔：\n\n{news_content}"
        
        try:
            result = self.glm_client.chat(
                prompt=prompt,
                system_prompt="你是一个专业的关键词提取助手，只返回关键词，用逗号分隔。",
                temperature=0.2
            )
            
            # 解析关键词
            keywords = [kw.strip() for kw in result.split(',') if kw.strip()]
            return keywords[:num_keywords]  # 确保不超过指定数量
            
        except GLMError as e:
            print(f"关键词提取失败: {e}")
            return []


class StockAnalyzer:
    """股票分析器示例类"""
    
    def __init__(self, api_key: str):
        self.glm_client = create_glm_client(api_key)
    
    def analyze_stock_news_impact(self, stock_name: str, news_content: str) -> dict:
        """
        分析新闻对特定股票的影响
        
        Args:
            stock_name (str): 股票名称
            news_content (str): 新闻内容
            
        Returns:
            dict: 分析结果
        """
        prompt = f"""
        请分析以下新闻对股票"{stock_name}"可能产生的影响：
        
        新闻内容：{news_content}
        
        请从以下几个方面进行分析：
        1. 影响程度（高/中/低）
        2. 影响方向（正面/负面/中性）
        3. 影响时间（短期/中期/长期）
        4. 简要分析原因
        
        请用JSON格式返回结果。
        """
        
        try:
            result = self.glm_client.chat(
                prompt=prompt,
                system_prompt="你是一个专业的股票分析师，擅长分析新闻对股票的影响。",
                temperature=0.3
            )
            
            # 这里可以进一步解析JSON结果
            return {"raw_analysis": result}
            
        except GLMError as e:
            print(f"股票影响分析失败: {e}")
            return {"error": str(e)}


class ChatAssistant:
    """聊天助手示例类"""
    
    def __init__(self, api_key: str):
        self.glm_client = create_glm_client(api_key)
        self.conversation_history = []
    
    def chat(self, user_input: str, context: str = None) -> str:
        """
        进行对话
        
        Args:
            user_input (str): 用户输入
            context (str): 上下文信息
            
        Returns:
            str: AI回复
        """
        # 构建系统提示
        system_prompt = "你是一个友好、专业的AI助手。"
        if context:
            system_prompt += f" 当前上下文：{context}"
        
        try:
            response = self.glm_client.chat(
                prompt=user_input,
                system_prompt=system_prompt,
                temperature=0.7
            )
            
            # 记录对话历史
            self.conversation_history.append({
                "user": user_input,
                "assistant": response
            })
            
            return response
            
        except GLMError as e:
            error_msg = f"对话失败: {e}"
            print(error_msg)
            return error_msg
    
    def get_conversation_summary(self) -> str:
        """获取对话摘要"""
        if not self.conversation_history:
            return "暂无对话记录"
        
        # 构建对话文本
        conversation_text = ""
        for item in self.conversation_history[-5:]:  # 只取最近5轮对话
            conversation_text += f"用户: {item['user']}\n助手: {item['assistant']}\n\n"
        
        prompt = f"请为以下对话生成一个简洁的摘要：\n\n{conversation_text}"
        
        try:
            return self.glm_client.chat(
                prompt=prompt,
                system_prompt="你是一个专业的对话摘要生成助手。",
                temperature=0.3
            )
        except GLMError as e:
            return f"摘要生成失败: {e}"


# 使用示例
def example_usage():
    """使用示例"""
    api_key = "696316314d42490c818e3e5ded184701.xhgEGx1ZbrAsBFy7"
    
    # 1. 新闻分析示例
    print("=== 新闻分析示例 ===")
    news_analyzer = NewsAnalyzer(api_key)
    
    sample_news = "某科技公司发布了革命性的新产品，预计将改变整个行业格局"
    
    sentiment = news_analyzer.analyze_sentiment(sample_news)
    print(f"情感分析: {sentiment}")
    
    summary = news_analyzer.summarize_news(sample_news, 50)
    print(f"新闻摘要: {summary}")
    
    keywords = news_analyzer.extract_keywords(sample_news, 3)
    print(f"关键词: {keywords}")
    
    # 2. 聊天助手示例
    print("\n=== 聊天助手示例 ===")
    chat_assistant = ChatAssistant(api_key)
    
    response1 = chat_assistant.chat("你好，请介绍一下人工智能")
    print(f"回复1: {response1[:100]}...")
    
    response2 = chat_assistant.chat("AI在金融领域有哪些应用？")
    print(f"回复2: {response2[:100]}...")
    
    summary = chat_assistant.get_conversation_summary()
    print(f"对话摘要: {summary[:100]}...")


if __name__ == "__main__":
    example_usage()
